{"version": "1.0", "created_at": "2025-01-01T00:00:00Z", "port_range": {"start": 8002, "end": 8100, "reserved_ports": [8100]}, "allocated_ports": {"8002": {"algorithm": "renchef<PERSON>", "allocated_at": "2025-01-01T00:00:00Z", "status": "active", "display_name": "人车非检测算法"}, "8003": {"algorithm": "we<PERSON><PERSON>_face", "allocated_at": "2025-01-01T00:00:00Z", "status": "active", "display_name": "温州人脸识别算法"}, "8004": {"algorithm": "accident_classify", "allocated_at": "2025-01-01T00:00:00Z", "status": "active", "display_name": "交通事故分类算法"}, "8005": {"algorithm": "wenzhou_gaokongpaowu", "allocated_at": "2025-08-04T00:00:00Z", "status": "active", "display_name": "温州高空抛物检测算法", "description": "智能自适应算法包：自动检测环境并选择最优执行模式（DeepStream/Python）", "features": ["auto-detection", "dual-engine", "smart-switching", "gpu-acceleration", "cpu-compatible"], "engine_modes": ["deepstream", "python"], "performance": {"deepstream_mode": "60+ FPS, 15路并发", "python_mode": "30 FPS, 1路处理"}}}, "next_available_port": 8006, "metadata": {"total_allocated": 4, "last_updated": "2025-08-04T00:00:00Z", "update_count": 7, "smart_algorithms": ["wenzhou_gaokongpaowu"], "restructure_info": {"date": "2025-08-04T00:00:00Z", "action": "consolidated smart algorithm package", "removed_ports": ["8006", "8007"], "unified_port": "8005"}}}