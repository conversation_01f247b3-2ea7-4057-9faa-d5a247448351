#include "deepstream_app.h"
#include "deepstream_config_file_parser.h"
#include "nvds_version.h"
#include <unistd.h>
#include <termios.h>
#include "opencv2/imgproc/imgproc.hpp"
#include "opencv2/highgui/highgui.hpp"

// 录制函数
void smart_record_handle(
    NvDsSrcBin *src_bin,
    guint source_id,
    std::string &video_path)
{
    NvDsSRSessionId sessId = 0;
    NvDsSRStatus st;
    guint startTime = 7;
    guint duration = 8;
    gchar *filename;
    // g_print("index: %d\n", index);
    if (src_bin->config->smart_rec_duration >= 0)
    {
        duration = src_bin->config->smart_rec_duration;
    }

    if (src_bin->config->smart_rec_start_time >= 0)
    {
        startTime = src_bin->config->smart_rec_start_time;
    }

    if (src_bin->recordCtx && !src_bin->reconfiguring)
    {
        NvDsSRContext *ctx = (NvDsSRContext *)src_bin->recordCtx;

        if (!ctx->recordOn)
        {
            g_print("Recording started..\n");
            st = NvDsSRStart(ctx, &sessId, startTime, duration, NULL);
            g_object_get(G_OBJECT(ctx->filesink), "location", &filename, NULL);
            if (st != NVDSSR_STATUS_OK)
                g_printerr("Unable to start recording, status=%d, recordOn=%d, resetDone=%d\n", st, ctx->recordOn, ctx->resetDone);
        }
        else
        {
            g_object_get(G_OBJECT(ctx->filesink), "location", &filename, NULL);
        }
        video_path = filename; // It will do copy
    }
    else
    {
        video_path = "";
    }
}

// 保存整张图片
void save_full_frame_image(
    NvDsObjEncCtxHandle obj_ctx_handle,
    NvBufSurface *ip_surf,
    NvDsFrameMeta *frame_meta,
    int width,
    int height,
    std::string filename)
{
    NvDsObjEncUsrArgs userData{0};
    userData.saveImg = TRUE;
    strncpy(userData.fileNameImg, filename.c_str(), filename.size());
    userData.attachUsrMeta = FALSE;
    userData.objNum = 1;
    NvDsObjectMeta obj_frame;
    obj_frame.rect_params.left = 0;
    obj_frame.rect_params.top = 0;
    obj_frame.rect_params.width = width;
    obj_frame.rect_params.height = height;
    nvds_obj_enc_process(obj_ctx_handle, &userData, ip_surf, &obj_frame, frame_meta);
    nvds_obj_enc_finish(obj_ctx_handle);
}

// 保存目标图片
void save_object_image(
    NvDsObjEncCtxHandle obj_ctx_handle,
    NvBufSurface *ip_surf,
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::string filename)
{
    NvDsObjEncUsrArgs userData = {0};
    /* To be set by user */
    userData.saveImg = TRUE;
    userData.attachUsrMeta = FALSE;
    /* Set if Image scaling Required */
    userData.scaleImg = FALSE;
    userData.scaledWidth = 0;
    userData.scaledHeight = 0;
    /* Preset */
    userData.objNum = 1;
    /* Quality */
    userData.quality = 95;
    strncpy(userData.fileNameImg, filename.c_str(), filename.size());
    /*Main Function Call */
    nvds_obj_enc_process(obj_ctx_handle, &userData, ip_surf, obj_meta, frame_meta);
    nvds_obj_enc_finish(obj_ctx_handle);
}

gpointer meta_copy_func(gpointer data, gpointer user_data)
{
    NvDsUserMeta *user_meta = (NvDsUserMeta *)data;
    NvDsEventMsgMeta *srcMeta = (NvDsEventMsgMeta *)user_meta->user_meta_data;
    NvDsEventMsgMeta *dstMeta = NULL;

    dstMeta = g_memdup(srcMeta, sizeof(NvDsEventMsgMeta));

    if (srcMeta->videoPath)
    {
        dstMeta->videoPath = g_strdup(srcMeta->videoPath); // 视频路径
    }
    if (srcMeta->otherAttrs)
    {
        dstMeta->otherAttrs = g_strdup(srcMeta->otherAttrs); // 全图路径
    }
    return dstMeta;
}

void meta_free_func(gpointer data, gpointer user_data)
{
    NvDsUserMeta *user_meta = (NvDsUserMeta *)data;
    NvDsEventMsgMeta *srcMeta = (NvDsEventMsgMeta *)user_meta->user_meta_data;

    if (srcMeta->videoPath)
    {
        g_free(srcMeta->videoPath);
    }
    if (srcMeta->otherAttrs)
    {
        g_free(srcMeta->otherAttrs);
    }
    g_free(user_meta->user_meta_data);
    user_meta->user_meta_data = NULL;
}

// 添加消息
void add_event_msg_to_frame(NvDsBatchMeta *batch_meta,
                            NvDsFrameMeta *frame_meta,
                            guint64 object_id,
                            NvDsEventType event_type,
                            NvDsRect bbox,
                            std::string video_path,
                            std::string full_image_path)
{
    // 生成事件消息
    NvDsEventMsgMeta *msg_meta = (NvDsEventMsgMeta *)g_malloc0(sizeof(NvDsEventMsgMeta));
    msg_meta->type = event_type; // 事件类型
    // bbox保存事件的发生区域，可以是车辆、行人或者是拥堵区域等
    msg_meta->bbox.top = bbox.top;
    msg_meta->bbox.left = bbox.left;
    msg_meta->bbox.width = bbox.width;
    msg_meta->bbox.height = bbox.height;
    msg_meta->trackingId = object_id;
    msg_meta->videoPath = g_strdup(video_path.c_str());       // 视频路径
    msg_meta->otherAttrs = g_strdup(full_image_path.c_str()); // 全图路径
    NvDsUserMeta *user_event_meta = nvds_acquire_user_meta_from_pool(batch_meta);
    if (user_event_meta)
    {
        user_event_meta->user_meta_data = (void *)msg_meta;
        user_event_meta->base_meta.meta_type = NVDS_EVENT_MSG_META;
        user_event_meta->base_meta.copy_func = (NvDsMetaCopyFunc)meta_copy_func;
        user_event_meta->base_meta.release_func = (NvDsMetaReleaseFunc)meta_free_func;
        nvds_add_user_meta_to_frame(frame_meta, user_event_meta);
    }
}

// 判断点是否多边形内
bool point_in_polygon(
    const std::pair<double, double> &point,
    const std::vector<std::pair<double, double>> &pt_polygon)
{
    // 交点个数
    int nCross = 0;
    int nCount = pt_polygon.size();
    for (int i = 0; i < nCount; i++)
    {
        std::pair<double, double> p1 = pt_polygon[i];
        std::pair<double, double> p2 = pt_polygon[(i + 1) % nCount]; // 点P1与P2形成连线

        // 如果两点水平，则射线要么与其无交点，要么有无数个，直接忽略
        if (p1.second == p2.second)
            continue;
        // 如果point坐标在两点y轴坐标之外，忽略，并且忽略射线正好穿过P1或者P2,那么这个交点会被算作2次的情况
        if (point.second < std::min(p1.second, p2.second))
            continue;
        if (point.second >= std::max(p1.second, p2.second))
            continue;
        // 求交点的x坐标（由直线两点式方程转化而来）
        double x = (double)(point.second - p1.second) * (double)(p2.first - p1.first) / (double)(p2.second - p1.second) + p1.first;

        if (x > point.first)
        {
            nCross++;
        }
    }
    // 交点为偶数，点在多边形之外
    // 交点为奇数，点在多边形之内
    if ((nCross % 2) == 1)
    {
        return true;
    }
    else
    {
        return false;
    }
}

// 录制并截图
void record_and_shot_image(
    NvDsObjEncCtxHandle obj_ctx_handle,
    NvBufSurface *ip_surf,
    NvDsBatchMeta *batch_meta,
    NvDsFrameMeta *frame_meta,
    NvDsSrcParentBin *bin,
    NvDsRect bbox,
    const std::vector<std::vector<std::pair<double, double>>> &source_areas,
    std::string full_image_dir,
    guint camera_id,
    guint64 object_id,
    NvDsEventType event_type)
{
    std::string video_path;
    std::string full_image_path;
    float left, top, width, height;
    full_image_path = full_image_dir + "/sensor_" + std::to_string(camera_id) +
                      "_frame_" + std::to_string(frame_meta->frame_num) + "_event_" + std::to_string(event_type) + ".jpg";
    save_full_frame_image(
        obj_ctx_handle,
        ip_surf,
        frame_meta,
        frame_meta->pipeline_width,
        frame_meta->pipeline_height,
        full_image_path);

    cv::Mat full_image = cv::imread(full_image_path);
    left = bbox.left;
    top = bbox.top;
    width = bbox.width;
    height = bbox.height;
    if (event_type == NVDS_EVENT_JAM)
    {
        for (const std::vector<std::pair<double, double>> &source_area : source_areas)
        {
            std::vector<cv::Point> pts;
            for (const std::pair<double, double> &pt : source_area)
            {
                pts.emplace_back(cv::Point(int(pt.first), int(pt.second)));
            }
            cv::polylines(full_image, pts, true, cv::Scalar(0, 255, 0), 4, cv::LINE_4, 0);
        }
        //设置绘制文本的相关参数
        std::string text = "Traffic congestion!";
        int font_face = cv::FONT_HERSHEY_COMPLEX;
        double font_scale = 2;
        int thickness = 2;
        int baseline;
        //获取文本框的长宽
        cv::Size text_size = cv::getTextSize(text, font_face, font_scale, thickness, &baseline);
        cv::putText(full_image, text, cv::Point(int(width * 0.5), int(height * 0.5)), font_face, font_scale, cv::Scalar(0, 0, 255), thickness, 8, 0);
    }
    else if (event_type == NVDS_EVENT_CONSTRUCT)
    {
        for (const std::vector<std::pair<double, double>> &source_area : source_areas)
        {
            std::vector<cv::Point> pts;
            for (const std::pair<double, double> &pt : source_area)
            {
                pts.emplace_back(cv::Point(int(pt.first), int(pt.second)));
            }
            cv::polylines(full_image, pts, true, cv::Scalar(0, 255, 0), 4, cv::LINE_4, 0);
        }
        //设置绘制文本的相关参数
        std::string text = "Construction!";
        int font_face = cv::FONT_HERSHEY_COMPLEX;
        double font_scale = 2;
        int thickness = 2;
        int baseline;
        //获取文本框的长宽
        cv::Size text_size = cv::getTextSize(text, font_face, font_scale, thickness, &baseline);
        cv::putText(full_image, text, cv::Point(int(width * 0.5), int(height * 0.5)), font_face, font_scale, cv::Scalar(0, 0, 255), thickness, 8, 0);
    }
    else
    {
        cv::rectangle(
            full_image,
            cv::Point(int(left), int(top)),
            cv::Point(int(left + width),
                      int(top + height)),
            cv::Scalar(0, 0, 255),
            cv::LINE_4);
        cv::arrowedLine(
            full_image,
            cv::Point(int(left - width * 0.5), int(top - height * 0.5)),
            cv::Point(int(left + width * 0.2), int(top + height * 0.2)),
            cv::Scalar(0, 0, 255), 4, 8, 0, 0.1);
        for (const std::vector<std::pair<double, double>> &source_area : source_areas)
        {
            std::vector<cv::Point> pts;
            for (const std::pair<double, double> &pt : source_area)
            {
                pts.emplace_back(cv::Point(int(pt.first), int(pt.second)));
            }
            cv::polylines(full_image, pts, true, cv::Scalar(0, 255, 0), 4, cv::LINE_4, 0);
        }
    }

    cv::imwrite(full_image_path, full_image);
    NvDsSrcBin *src_bin = &bin->sub_bins[frame_meta->source_id];
    smart_record_handle(src_bin, frame_meta->source_id, video_path);
    add_event_msg_to_frame(
        batch_meta,
        frame_meta,
        object_id,
        event_type,
        bbox,
        video_path,
        full_image_path);
}

// 获取车辆坐标
std::pair<float, float> get_bottom_center_point(NvDsFrameMeta *frame_meta, NvDsObjectMeta *obj_meta)
{
    float scaleW = (float)frame_meta->source_frame_width /
                           (frame_meta->pipeline_width == 0)
                       ? 1
                       : frame_meta->pipeline_width;
    float scaleH = (float)frame_meta->source_frame_height /
                           (frame_meta->pipeline_height == 0)
                       ? 1
                       : frame_meta->pipeline_height;
    float left = obj_meta->rect_params.left * scaleW;
    float top = obj_meta->rect_params.top * scaleH;
    float width = obj_meta->rect_params.width * scaleW;
    float height = obj_meta->rect_params.height * scaleH;

    std::pair<float, float> point;
    //取目标box底部中心点
    point.first = left + width / 2;
    point.second = top + height;
    return point;
}

double calculate_angle(std::vector<double> line1, std::vector<double> line2)
{
    double x1 = line1[0];
    double y1 = line1[1];
    double x2 = line1[2];
    double y2 = line1[3];
    double x3 = line2[0];
    double y3 = line2[1];
    double x4 = line2[2];
    double y4 = line2[3];

    // 计算夹角
    double angle = acos(((x2 - x1) * (x4 - x3) + (y2 - y1) * (y4 - y3)) / (sqrt(pow(x2 - x1, 2) + pow(y2 - y1, 2)) * sqrt(pow(x4 - x3, 2) + pow(y4 - y3, 2))));
    return angle * 180 / 3.1415926;
}

// 0.车辆压实线事件
bool vehicle_compaction_line(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint times,
    gdouble thres)
{

    gboolean ret = FALSE;
    static cache::lru_cache<std::string, int> vehicle_compaction_line_lru(500);
    //如果非车辆类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_VEHICLE)
    {
        return ret;
    }

    std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
    for (auto polygon : polygons)
    {
        if (point_in_polygon(point, polygon))
        {
            std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
            if (!vehicle_compaction_line_lru.exists(sensor_obj))
            {
                vehicle_compaction_line_lru.put(sensor_obj, 1);
                ret = TRUE;
                return ret;
            }
            else
            {
                vehicle_compaction_line_lru.put(sensor_obj, 1);
            }
        }
    }
    return ret;
}

// 0.车辆压实线事件 v3
bool vehicle_compaction_line_v3(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint repeat_times,
    gdouble vehicle_conf_thres = 0.5,
    gint duration = 0)
{

    gboolean ret = FALSE;
    int times = std::max(repeat_times, duration * 25);
    static cache::lru_cache<std::string, int> vehicle_compaction_line_lru(500);
    //如果非车辆类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_VEHICLE)
    {
        return ret;
    }

    std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
    for (auto polygon : polygons)
    {
        if (point_in_polygon(point, polygon))
        {
            std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
            if (!vehicle_compaction_line_lru.exists(sensor_obj))
            {
                vehicle_compaction_line_lru.put(sensor_obj, 1);
            }
            else
            {
                vehicle_compaction_line_lru.put(sensor_obj, vehicle_compaction_line_lru.get(sensor_obj) + 1);
            }
            // 第 times 次出现，进行告警
            if (vehicle_compaction_line_lru.get(sensor_obj) == times)
            {
                ret = TRUE;
                return ret;
            }
        }
    }
    return ret;
}

// 1. 施工检测
bool construction_detect(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint times,
    gdouble thres)
{

    gboolean ret = FALSE;
    static cache::lru_cache<std::string, int> construction_detect_lru(500);
    //如果不是非锥桶类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_CONE)
    {
        return ret;
    }
    if (obj_meta->confidence < thres)
    {
        return ret;
    }

    std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
    for (auto polygon : polygons)
    {
        if (point_in_polygon(point, polygon))
        {
            std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
            if (!construction_detect_lru.exists(sensor_obj))
            {
                construction_detect_lru.put(sensor_obj, 1);
            }
            else
            {
                construction_detect_lru.put(sensor_obj, construction_detect_lru.get(sensor_obj) + 1);
            }
            // 第 times 次出现，进行告警
            if (construction_detect_lru.get(sensor_obj) == times)
            {
                ret = TRUE;
                return ret;
            }
        }
    }
    return ret;
}

// 1. 施工检测 v3版本
bool construction_detect_v3(
    NvDsFrameMeta *frame_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint repeat_times = 5,
    gint person_count_thres = 1,
    gint cone_count_thres = 1,
    gint construction_car_count_thres = 1,
    gdouble person_conf_thres = 0.5,
    gdouble cone_conf_thres = 0.5,
    gdouble construction_car_conf_thres = 0.5,
    gint duration = 0,
    gint interval = 0)
{

    gboolean ret = FALSE;
    int times = std::max(repeat_times, duration * 25);
    static cache::lru_cache<std::string, std::pair<guint64, int>> construction_detect_lru(500);
    guint64 msg_interval = 0.5 * 60 * 60;
    struct timespec ts
    {
    };
    clock_gettime(CLOCK_REALTIME, &ts);
    int person_count = 0;
    int cone_count = 0;
    int construction_car_count = 0;
    // 统计区域内锥桶、人、车数量
    for (NvDsObjectMetaList *obj_l = frame_meta->obj_meta_list; obj_l; obj_l = obj_l->next)
    {
        NvDsObjectMeta *obj_meta = (NvDsObjectMeta *)obj_l->data;
        if (obj_meta == NULL || obj_meta->unique_component_id == SECONDARY_DETECTOR_UID)
        {
            continue;
        }
        std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
        bool in_polygon = false;
        for (auto polygon : polygons)
        {
            if (point_in_polygon(point, polygon))
            {
                in_polygon = true;
            }
        }
        if (in_polygon)
        {
            if (obj_meta->class_id == PGIE_CLASS_ID_CONE && obj_meta->confidence > cone_conf_thres)
            {
                cone_count++;
            }
            if (obj_meta->class_id == PGIE_CLASS_ID_VEHICLE && obj_meta->confidence > construction_car_conf_thres)
            {
                construction_car_count++;
            }
            if (obj_meta->class_id == PGIE_CLASS_ID_PERSON && obj_meta->confidence > person_conf_thres)
            {
                person_count++;
            }
        }
    }
    // 数量满足情况
    if (cone_count >= cone_count_thres)
    {
        if (person_count >= person_count_thres || construction_car_count >= construction_car_count_thres)
        {
            std::string source_id = std::to_string(frame_meta->source_id);
            // 如果不存在key，添加一个新的，次数为0; 另一种情况，距离上次发生事件已经很久了，也重新开始
            if (!construction_detect_lru.exists(source_id) || ts.tv_sec - construction_detect_lru.get(source_id).first > interval)
            {
                construction_detect_lru.put(source_id, std::make_pair(ts.tv_sec, 0));
            }
            //第 times 次出现，且时间间隔小于阈值，进行告警
            if (construction_detect_lru.get(source_id).second + 1 == times)
            {
                ret = TRUE;
            }
            construction_detect_lru.put(source_id, std::make_pair(ts.tv_sec, construction_detect_lru.get(source_id).second + 1));
        }
    }

    return ret;
}

// 2. 异常停车
bool abnormal_parking2(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint times,
    gdouble thres)
{

    gboolean ret = FALSE;
    static cache::lru_cache<std::string, std::pair<guint64, int>> abnormal_parking_lru(500);
    struct timespec ts
    {
    };
    clock_gettime(CLOCK_REALTIME, &ts);
    guint64 abnormal_parking_threshold = 1 * 5;
    //如果非车辆类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_VEHICLE)
    {
        return ret;
    }

    std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
    for (auto polygon : polygons)
    {
        if (point_in_polygon(point, polygon))
        {
            std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
            if (!abnormal_parking_lru.exists(sensor_obj))
            {
                // 第一次追踪到目标
                abnormal_parking_lru.put(sensor_obj, std::make_pair(ts.tv_sec, 0));
                return ret;
            }
            else
            {
                // 再次追踪到目标，更新LRU
                abnormal_parking_lru.put(sensor_obj, abnormal_parking_lru.get(sensor_obj));
            }
            if (ts.tv_sec - abnormal_parking_lru.get(sensor_obj).first < abnormal_parking_threshold)
            {
                // 追踪到的目标时间未到阈值
                return ret;
            }
            if (abnormal_parking_lru.get(sensor_obj).second == 1)
            {
                // 保证消息只发一次
                return ret;
            }
            std::pair<guint64, int> tmp_pair = abnormal_parking_lru.get(sensor_obj);
            tmp_pair.second = 1;
            abnormal_parking_lru.put(sensor_obj, tmp_pair);
            ret = TRUE;
            guint64 parking_duration = ts.tv_sec - abnormal_parking_lru.get(sensor_obj).first;
            return ret;
        }
    }
    return ret;
}

gboolean object_moved(std::pair<float, float> pre_pts, std::pair<float, float> &current_pts, float dis_thr = 1)
{
    gboolean has_moved = FALSE;
    float dist;
    dist = sqrt((current_pts.first - pre_pts.first) * (current_pts.first - pre_pts.first) + (current_pts.second - pre_pts.second) * (current_pts.second - pre_pts.second));
    // printf("dist: %f\n",dist );
    if (dist > dis_thr)
    {
        has_moved = TRUE;
    }
    return has_moved;
}

// 2. 异常停车第二版
bool abnormal_parking(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint times,
    gdouble thres)
{

    gboolean ret = FALSE;
    static cache::lru_cache<std::string, std::pair<std::pair<float, float>, std::pair<int, int>>> abnormal_parking_lru(500);
    //如果非车辆类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_VEHICLE)
    {
        return ret;
    }

    std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
    for (auto polygon : polygons)
    {
        if (point_in_polygon(point, polygon))
        {
            std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
            if (!abnormal_parking_lru.exists(sensor_obj))
            {
                // 第一次追踪到目标
                abnormal_parking_lru.put(sensor_obj, std::make_pair(point, std::pair<int, int>(0, 0)));
                return ret;
            }
            if (object_moved(abnormal_parking_lru.get(sensor_obj).first, point))
            {
                //如果目标动了，重置计数
                std::pair<int, int> tmp_pair = abnormal_parking_lru.get(sensor_obj).second;
                tmp_pair.first = 0;
                abnormal_parking_lru.put(sensor_obj, std::make_pair(point, tmp_pair));
            }
            else
            {
                //如果目标没动，累加计速
                std::pair<int, int> tmp_pair = abnormal_parking_lru.get(sensor_obj).second;
                tmp_pair.first += 1;
                abnormal_parking_lru.put(sensor_obj, std::make_pair(point, tmp_pair));
            }
            // 第 times 次出现，进行告警
            if (abnormal_parking_lru.get(sensor_obj).second.first == times && abnormal_parking_lru.get(sensor_obj).second.second != 1)
            {
                // 更新告警状态，保证只告警一次
                ret = TRUE;
                std::pair<int, int> tmp_pair = abnormal_parking_lru.get(sensor_obj).second;
                tmp_pair.second = 1;
                abnormal_parking_lru.put(sensor_obj, std::make_pair(point, tmp_pair));
                return ret;
            }
        }
    }
    return ret;
}

// 2. 异常停车 v3
bool abnormal_parking_v3(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint repeat_times,
    gdouble vehicle_conf_thres = 0.5,
    gint duration = 0)
{
    gboolean ret = FALSE;
    int times = std::max(repeat_times, duration * 25);
    static cache::lru_cache<std::string, std::pair<std::pair<float, float>, std::pair<int, int>>> abnormal_parking_lru(500);
    //如果非车辆类型，直接return
    if (obj_meta->class_id == PGIE_CLASS_ID_VEHICLE && obj_meta->confidence > vehicle_conf_thres)
    {

        std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
        for (auto polygon : polygons)
        {
            if (point_in_polygon(point, polygon))
            {
                std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
                if (!abnormal_parking_lru.exists(sensor_obj))
                {
                    // 第一次追踪到目标
                    abnormal_parking_lru.put(sensor_obj, std::make_pair(point, std::pair<int, int>(0, 0)));
                    return ret;
                }
                if (object_moved(abnormal_parking_lru.get(sensor_obj).first, point))
                {
                    //如果目标动了，重置计数
                    std::pair<int, int> tmp_pair = abnormal_parking_lru.get(sensor_obj).second;
                    tmp_pair.first = 0;
                    abnormal_parking_lru.put(sensor_obj, std::make_pair(point, tmp_pair));
                }
                else
                {
                    //如果目标没动，累加计速
                    std::pair<int, int> tmp_pair = abnormal_parking_lru.get(sensor_obj).second;
                    tmp_pair.first += 1;
                    abnormal_parking_lru.put(sensor_obj, std::make_pair(point, tmp_pair));
                }
                // 第 times 次出现，进行告警
                if (abnormal_parking_lru.get(sensor_obj).second.first == times && abnormal_parking_lru.get(sensor_obj).second.second != 1)
                {
                    // 更新告警状态，保证只告警一次
                    ret = TRUE;
                    std::pair<int, int> tmp_pair = abnormal_parking_lru.get(sensor_obj).second;
                    tmp_pair.second = 1;
                    abnormal_parking_lru.put(sensor_obj, std::make_pair(point, tmp_pair));
                    return ret;
                }
            }
        }
    }
    return ret;
}

// 3. 逆行车辆
bool vehicle_reverse_direction_v1(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint times,
    gdouble thres)
{
    gboolean ret = FALSE;
    static cache::lru_cache<std::string, int> vehicle_reverse_direction_lru(500);
    //如果非车辆类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_VEHICLE)
    {
        return ret;
    }
    for (NvDsMetaList *l_user_meta = obj_meta->obj_user_meta_list; l_user_meta != NULL;
         l_user_meta = l_user_meta->next)
    {
        NvDsUserMeta *user_meta = (NvDsUserMeta *)(l_user_meta->data);
        if (user_meta->base_meta.meta_type == NVDS_USER_OBJ_META_NVDSANALYTICS)
        {

            NvDsAnalyticsObjInfo *user_meta_data =
                (NvDsAnalyticsObjInfo *)user_meta->user_meta_data;
            if (!user_meta_data->dirStatus.empty())
            {
                for (int i = 0; i < polygons.size(); i++)
                {
                    std::string::size_type direction_idx = user_meta_data->dirStatus.find("reverse" + std::to_string(i));
                    if (direction_idx != std::string::npos)
                    {
                        std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
                        if (point_in_polygon(point, polygons[i]))
                        {
                            std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
                            if (!vehicle_reverse_direction_lru.exists(sensor_obj))
                            {
                                vehicle_reverse_direction_lru.put(sensor_obj, 1);
                                ret = TRUE;
                                return ret;
                            }
                            else
                            {
                                vehicle_reverse_direction_lru.put(sensor_obj, 1);
                            }
                        }
                    }
                }
            }
        }
    }
    return ret;
}

// 3. 逆行车辆 v2
bool vehicle_reverse_direction_v2(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    std::vector<std::vector<double>> &directions,
    gint times,
    gdouble thres)
{
    gboolean ret = FALSE;
    static cache::lru_cache<std::string, int> vehicle_reverse_direction_lru(500);

    static cache::lru_cache<std::string, std::queue<std::pair<float, float>>> vehicle_position_lru(500);
    std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);

    //如果非车辆类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_VEHICLE)
    {
        return ret;
    }
    std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);

    // 如果队列不存在，则新建队列并存入缓存
    if (!vehicle_position_lru.exists(sensor_obj))
    {
        std::queue<std::pair<float, float>> point_queue;
        vehicle_position_lru.put(sensor_obj, point_queue);
    }
    // 获取当前点队列
    std::queue<std::pair<float, float>> cur_obj_points = vehicle_position_lru.get(sensor_obj);

    // 如果点的个数小于25，一直添加
    if (cur_obj_points.size() < 50)
    {
        cur_obj_points.push(point);
        vehicle_position_lru.put(sensor_obj, cur_obj_points);
        return ret;
    }
    else
    {
        // 否则，添加一个点，并出队一个点
        cur_obj_points.push(point);
        std::pair<float, float> prev = cur_obj_points.front();
        for (int i = 0; i < polygons.size(); i++)
        {
            // 当前区域对应的方向 x,y => x,y
            std::vector<double> direction = directions[i];
            std::vector<double> cur_direction;
            cur_direction.emplace_back(prev.first);
            cur_direction.emplace_back(prev.second);
            cur_direction.emplace_back(point.first);
            cur_direction.emplace_back(point.second);
            double distance = sqrt(pow(point.first - prev.first, 2) + pow(point.second - prev.second, 2));
            // 运动距离小于50像素
            if (distance < 50)
            {
                continue;
            }
            // 计算两条线夹角
            double angle = calculate_angle(direction, cur_direction);

            // 如果夹角小于90度，说明有逆行，再判断是否在区域内
            if (angle < 90 && point_in_polygon(point, polygons[i]))
            {
                if (!vehicle_reverse_direction_lru.exists(sensor_obj))
                {
                    vehicle_reverse_direction_lru.put(sensor_obj, 1);
                    ret = TRUE;
                    return ret;
                }
                else
                {
                    vehicle_reverse_direction_lru.put(sensor_obj, 1);
                }
            }
        }
        // 删除队首
        cur_obj_points.pop();
        vehicle_position_lru.put(sensor_obj, cur_obj_points);
    }

    return ret;
}

// 3. 逆行车辆 v3
bool vehicle_reverse_direction_v3(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    std::vector<std::vector<double>> &directions,
    gint repeat_times,
    gdouble vehicle_conf_thres = 0.5,
    gint duration = 0)
{
    gboolean ret = FALSE;
    int times = std::max(repeat_times, duration * 25);
    static cache::lru_cache<std::string, int> vehicle_reverse_direction_lru(500);

    static cache::lru_cache<std::string, std::queue<std::pair<float, float>>> vehicle_position_lru(500);
    std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);

    //如果非车辆类型，直接return
    if (obj_meta->class_id == PGIE_CLASS_ID_VEHICLE && obj_meta->confidence > vehicle_conf_thres)
    {
        std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);

        // 如果队列不存在，则新建队列并存入缓存
        if (!vehicle_position_lru.exists(sensor_obj))
        {
            std::queue<std::pair<float, float>> point_queue;
            vehicle_position_lru.put(sensor_obj, point_queue);
        }
        // 获取当前点队列
        std::queue<std::pair<float, float>> cur_obj_points = vehicle_position_lru.get(sensor_obj);

        // 如果点的个数小于25，一直添加
        if (cur_obj_points.size() < 50)
        {
            cur_obj_points.push(point);
            vehicle_position_lru.put(sensor_obj, cur_obj_points);
            return ret;
        }
        else
        {
            // 否则，添加一个点，并出队一个点
            cur_obj_points.push(point);
            std::pair<float, float> prev = cur_obj_points.front();
            for (int i = 0; i < polygons.size(); i++)
            {
                // 当前区域对应的方向 x,y => x,y
                std::vector<double> direction = directions[i];
                std::vector<double> cur_direction;
                cur_direction.emplace_back(prev.first);
                cur_direction.emplace_back(prev.second);
                cur_direction.emplace_back(point.first);
                cur_direction.emplace_back(point.second);
                double distance = sqrt(pow(point.first - prev.first, 2) + pow(point.second - prev.second, 2));
                // 运动距离小于50像素
                if (distance < 50)
                {
                    continue;
                }
                // 计算两条线夹角
                double angle = calculate_angle(direction, cur_direction);

                // 如果夹角小于90度，说明有逆行，再判断是否在区域内
                if (angle < 90 && point_in_polygon(point, polygons[i]))
                {
                    if (!vehicle_reverse_direction_lru.exists(sensor_obj))
                    {
                        vehicle_reverse_direction_lru.put(sensor_obj, 1);
                        ret = TRUE;
                        return ret;
                    }
                    else
                    {
                        vehicle_reverse_direction_lru.put(sensor_obj, 1);
                    }
                }
            }
            // 删除队首
            cur_obj_points.pop();
            vehicle_position_lru.put(sensor_obj, cur_obj_points);
        }
    }
    return ret;
}

// 4. 拥堵排队
bool traffic_congestion(
    NvDsFrameMeta *frame_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint vehicle_count_thres,
    gint times,
    gdouble thres)
{

    gboolean ret = FALSE;
    static cache::lru_cache<guint, guint64> traffic_congestion_lru(100);
    guint64 msg_interval = 0.5 * 60 * 60;
    struct timespec ts
    {
    };
    clock_gettime(CLOCK_REALTIME, &ts);
    int vehicle_count = 0;

    for (NvDsObjectMetaList *obj_l = frame_meta->obj_meta_list; obj_l; obj_l = obj_l->next)
    {
        NvDsObjectMeta *obj_meta = (NvDsObjectMeta *)obj_l->data;
        if (obj_meta == NULL || obj_meta->unique_component_id == SECONDARY_DETECTOR_UID)
        {
            // Ignore Null object.
            continue;
        }
        //如果非车辆类型，忽略
        if (obj_meta->class_id != PGIE_CLASS_ID_VEHICLE)
        {
            continue;
        }
        std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
        bool in_polygon = false;
        for (auto polygon : polygons)
        {
            if (point_in_polygon(point, polygon))
            {
                in_polygon = true;
            }
        }
        if (in_polygon)
            vehicle_count++;
    }

    if (vehicle_count > vehicle_count_thres)
    {
        if (!traffic_congestion_lru.exists(frame_meta->source_id))
        {
            traffic_congestion_lru.put(frame_meta->source_id, ts.tv_sec);
            ret = TRUE;
            return ret;
        }
        else if (ts.tv_sec - traffic_congestion_lru.get(frame_meta->source_id) >= msg_interval)
        {
            traffic_congestion_lru.put(frame_meta->source_id, ts.tv_sec);
            ret = TRUE;
            return ret;
        }
    }
    return ret;
}

// 4. 拥堵排队 v3
bool traffic_congestion_v3(
    NvDsFrameMeta *frame_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint vehicle_count_thres,
    gint repeat_times = 1,
    gdouble vehicle_conf_thres = 0.5,
    gint duration = 1,
    gint interval = 0)
{
    gboolean ret = FALSE;
    int times = std::max(repeat_times, duration * 25);
    static cache::lru_cache<std::string, std::pair<guint64, int>> traffic_congestion_lru(100);
    struct timespec ts
    {
    };
    clock_gettime(CLOCK_REALTIME, &ts);
    int vehicle_count = 0;
    for (NvDsObjectMetaList *obj_l = frame_meta->obj_meta_list; obj_l; obj_l = obj_l->next)
    {
        NvDsObjectMeta *obj_meta = (NvDsObjectMeta *)obj_l->data;
        if (obj_meta == NULL || obj_meta->unique_component_id == SECONDARY_DETECTOR_UID)
        {
            continue;
        }
        //如果非车辆类型，忽略
        if (obj_meta->class_id == PGIE_CLASS_ID_VEHICLE && obj_meta->confidence > vehicle_conf_thres)
        {

            std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
            bool in_polygon = false;
            for (auto polygon : polygons)
            {
                if (point_in_polygon(point, polygon))
                {
                    in_polygon = true;
                }
            }
            if (in_polygon)
                vehicle_count++;
        }
    }

    // 数量满足情况
    if (vehicle_count > vehicle_count_thres)
    {
        std::string source_id = std::to_string(frame_meta->source_id);
        // 如果不存在key，添加一个新的，次数为0; 另一种情况，距离上次发生事件已经很久了，也重新开始
        if (!traffic_congestion_lru.exists(source_id) || ts.tv_sec - traffic_congestion_lru.get(source_id).first > interval)
        {
            traffic_congestion_lru.put(source_id, std::make_pair(ts.tv_sec, 0));
        }
        //第 times 次出现，且时间间隔小于阈值，进行告警
        if (traffic_congestion_lru.get(source_id).second + 1 == times)
        {
            ret = TRUE;
        }
        traffic_congestion_lru.put(source_id, std::make_pair(ts.tv_sec, traffic_congestion_lru.get(source_id).second + 1));
    }
    return ret;
}

// 5. 行人穿越
bool person_on_driveway(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint times,
    gdouble thres)
{

    gboolean ret = FALSE;
    static cache::lru_cache<std::string, int> person_on_driveway_lru(500);
    //如果非行人，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_PERSON)
    {
        return ret;
    }
    // 过滤低置信度目标
    if (obj_meta->confidence < thres)
    {
        return ret;
    }

    std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
    for (auto polygon : polygons)
    {
        if (point_in_polygon(point, polygon))
        {
            std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
            if (!person_on_driveway_lru.exists(sensor_obj))
            {
                person_on_driveway_lru.put(sensor_obj, 1);
            }
            else
            {
                person_on_driveway_lru.put(sensor_obj, person_on_driveway_lru.get(sensor_obj) + 1);
            }
            // 第 times 次出现，进行告警
            if (person_on_driveway_lru.get(sensor_obj) == times)
            {
                ret = TRUE;
                return ret;
            }
        }
    }
    return ret;
}

// 5. 行人穿越 v3
bool person_on_driveway_v3(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint repeat_times = 1,
    gdouble person_conf_thres = 0.5,
    gint duration = 0)
{
    gboolean ret = FALSE;
    int times = std::max(repeat_times, duration * 25);
    static cache::lru_cache<std::string, int> person_on_driveway_lru(500);
    //如果非行人，直接return
    if (obj_meta->class_id == PGIE_CLASS_ID_PERSON && obj_meta->confidence > person_conf_thres)
    {
        std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
        for (auto polygon : polygons)
        {
            if (point_in_polygon(point, polygon))
            {
                std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
                if (!person_on_driveway_lru.exists(sensor_obj))
                {
                    person_on_driveway_lru.put(sensor_obj, 1);
                }
                else
                {
                    person_on_driveway_lru.put(sensor_obj, person_on_driveway_lru.get(sensor_obj) + 1);
                }
                // 第 times 次出现，进行告警
                if (person_on_driveway_lru.get(sensor_obj) == times)
                {
                    ret = TRUE;
                    return ret;
                }
            }
        }
    }
    return ret;
}

// 6. 货车检测
bool truck_detect(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint times,
    gdouble thres)
{

    gboolean ret = FALSE;
    static cache::lru_cache<std::string, int> truck_detect_lru(500);
    //如果非车辆类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_VEHICLE)
    {
        return ret;
    }
    // 过滤低置信度目标
    if (obj_meta->confidence < thres)
    {
        return ret;
    }
    //如果sgie中没有货车，直接return
    std::vector<unsigned int> truck_cls_ids;
    std::stringstream ss(VEHICLE_TYPE_TRUCK);

    while (ss.good())
    {
        std::string substr;
        getline(ss, substr, ',');
        truck_cls_ids.emplace_back(std::stoi(substr));
    }

    gboolean has_truck = FALSE;
    for (NvDsClassifierMetaList *cl = obj_meta->classifier_meta_list; cl; cl = cl->next)
    {
        NvDsClassifierMeta *cl_meta = (NvDsClassifierMeta *)cl->data;
        if (strcmp(cl_meta->classifier_type, VEHICLE_TYPE) == 0)
        {
            for (NvDsLabelInfoList *ll = cl_meta->label_info_list; ll; ll = ll->next)
            {
                NvDsLabelInfo *ll_meta = (NvDsLabelInfo *)ll->data;
                if (find(truck_cls_ids.begin(), truck_cls_ids.end(), ll_meta->result_class_id) != truck_cls_ids.end())
                {
                    // 过滤低置信度分类
                    if (ll_meta->result_prob > thres)
                    {
                        has_truck = TRUE;
                    }
                }
            }
        }
    }
    if (has_truck)
    {
        std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
        for (auto polygon : polygons)
        {
            if (point_in_polygon(point, polygon))
            {
                std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
                if (!truck_detect_lru.exists(sensor_obj))
                {
                    truck_detect_lru.put(sensor_obj, 1);
                }
                else
                {
                    truck_detect_lru.put(sensor_obj, truck_detect_lru.get(sensor_obj) + 1);
                }
                // 第 times 次出现，进行告警
                if (truck_detect_lru.get(sensor_obj) == times)
                {
                    ret = TRUE;
                    return ret;
                }
            }
        }
    }
    return ret;
}

// 6. 货车检测 v3
bool truck_detect_v3(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint repeat_times = 1,
    gdouble vehicle_conf_thres = 0.5,
    gdouble truck_conf_thres = 0.5,
    gint duration = 0)
{

    gboolean ret = FALSE;
    int times = std::max(repeat_times, duration * 25);
    static cache::lru_cache<std::string, int> truck_detect_lru(500);
    //如果非车辆类型，直接return
    if (obj_meta->class_id == PGIE_CLASS_ID_VEHICLE && obj_meta->confidence > vehicle_conf_thres)
    {
        //如果sgie中没有货车，直接return
        std::vector<unsigned int> truck_cls_ids;
        std::stringstream ss(VEHICLE_TYPE_TRUCK);

        while (ss.good())
        {
            std::string substr;
            getline(ss, substr, ',');
            truck_cls_ids.emplace_back(std::stoi(substr));
        }

        gboolean has_truck = FALSE;
        for (NvDsClassifierMetaList *cl = obj_meta->classifier_meta_list; cl; cl = cl->next)
        {
            NvDsClassifierMeta *cl_meta = (NvDsClassifierMeta *)cl->data;
            if (strcmp(cl_meta->classifier_type, VEHICLE_TYPE) == 0)
            {
                for (NvDsLabelInfoList *ll = cl_meta->label_info_list; ll; ll = ll->next)
                {
                    NvDsLabelInfo *ll_meta = (NvDsLabelInfo *)ll->data;
                    if (find(truck_cls_ids.begin(), truck_cls_ids.end(), ll_meta->result_class_id) != truck_cls_ids.end())
                    {
                        // 过滤低置信度分类
                        if (ll_meta->result_prob > truck_conf_thres)
                        {
                            has_truck = TRUE;
                        }
                    }
                }
            }
        }
        if (has_truck)
        {
            std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
            for (auto polygon : polygons)
            {
                if (point_in_polygon(point, polygon))
                {
                    std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
                    if (!truck_detect_lru.exists(sensor_obj))
                    {
                        truck_detect_lru.put(sensor_obj, 1);
                    }
                    else
                    {
                        truck_detect_lru.put(sensor_obj, truck_detect_lru.get(sensor_obj) + 1);
                    }
                    // 第 times 次出现，进行告警
                    if (truck_detect_lru.get(sensor_obj) == times)
                    {
                        ret = TRUE;
                        return ret;
                    }
                }
            }
        }
    }
    return ret;
}

// 7.危险品车检测
bool danger_detect(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint times,
    gdouble thres)
{
    gboolean ret = FALSE;
    static cache::lru_cache<std::string, int> danger_detect_lru(500);
    //如果非车辆类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_VEHICLE)
    {
        return ret;
    }
    // 筛选出所有车牌目标
    std::vector<NvDsObjectMeta *> secondary_objs; // 车牌检测目标
    NvDsObjectMeta *obj;
    for (NvDsMetaList *l_obj = frame_meta->obj_meta_list; l_obj; l_obj = l_obj->next)
    {
        obj = (NvDsObjectMeta *)l_obj->data;
        if (obj == NULL)
        {
            // Ignore Null object.
            continue;
        }
        if (obj->unique_component_id == SECONDARY_DETECTOR_UID)
        {
            secondary_objs.push_back(obj);
        }
    }
    for (auto secondary_obj : secondary_objs)
    {
        // 如果当前车辆有子目标且子目标类别大于0，说明有危险品标志
        if (secondary_obj->parent == obj_meta && secondary_obj->class_id > 0)
        {
            // 过滤低置信度目标
            if (secondary_obj->confidence < thres)
            {
                continue;
            }
            std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
            for (auto polygon : polygons)
            {
                if (point_in_polygon(point, polygon))
                {
                    std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
                    if (!danger_detect_lru.exists(sensor_obj))
                    {
                        danger_detect_lru.put(sensor_obj, 1);
                    }
                    else
                    {
                        danger_detect_lru.put(sensor_obj, danger_detect_lru.get(sensor_obj) + 1);
                    }
                    // 第 times 次出现，进行告警
                    if (danger_detect_lru.get(sensor_obj) == times)
                    {
                        ret = TRUE;
                        return ret;
                    }
                }
            }
        }
    }
    return ret;
}

// 7.危险品车检测 v3
bool danger_detect_v3(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint repeat_times = 1,
    gdouble vehicle_conf_thres = 0.5,
    gdouble danger_conf_thres = 0.5,
    gint duration = 0)
{
    gboolean ret = FALSE;
    int times = std::max(repeat_times, duration * 25);

    static cache::lru_cache<std::string, int> danger_detect_lru(500);
    //如果非车辆类型，直接return
    if (obj_meta->class_id == PGIE_CLASS_ID_VEHICLE && obj_meta->confidence > vehicle_conf_thres)
    {

        // 筛选出所有车牌目标
        std::vector<NvDsObjectMeta *> secondary_objs; // 车牌检测目标
        NvDsObjectMeta *obj;
        for (NvDsMetaList *l_obj = frame_meta->obj_meta_list; l_obj; l_obj = l_obj->next)
        {
            obj = (NvDsObjectMeta *)l_obj->data;
            if (obj == NULL)
            {
                // Ignore Null object.
                continue;
            }
            if (obj->unique_component_id == SECONDARY_DETECTOR_UID)
            {
                secondary_objs.push_back(obj);
            }
        }
        for (auto secondary_obj : secondary_objs)
        {
            // 如果当前车辆有子目标且子目标类别大于0，说明有危险品标志
            if (secondary_obj->parent == obj_meta && secondary_obj->class_id > 0)
            {
                // 过滤低置信度目标
                if (secondary_obj->confidence < danger_conf_thres)
                {
                    continue;
                }
                std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
                for (auto polygon : polygons)
                {
                    if (point_in_polygon(point, polygon))
                    {
                        std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
                        if (!danger_detect_lru.exists(sensor_obj))
                        {
                            danger_detect_lru.put(sensor_obj, 1);
                        }
                        else
                        {
                            danger_detect_lru.put(sensor_obj, danger_detect_lru.get(sensor_obj) + 1);
                        }
                        // 第 times 次出现，进行告警
                        if (danger_detect_lru.get(sensor_obj) == times)
                        {
                            ret = TRUE;
                            return ret;
                        }
                    }
                }
            }
        }
    }
    return ret;
}

// 8.车辆走应急车道
bool vehicle_on_nondriveway(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint times,
    gdouble thres)
{

    gboolean ret = FALSE;
    static cache::lru_cache<std::string, int> vehicle_on_nondriveway_lru(500);
    //如果非车辆类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_VEHICLE)
    {
        return ret;
    }

    std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
    for (auto polygon : polygons)
    {
        if (point_in_polygon(point, polygon))
        {
            std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
            if (!vehicle_on_nondriveway_lru.exists(sensor_obj))
            {
                vehicle_on_nondriveway_lru.put(sensor_obj, 1);
                ret = TRUE;
                return ret;
            }
            else
            {
                vehicle_on_nondriveway_lru.put(sensor_obj, 1);
            }
        }
    }
    return ret;
}

// 8.车辆走应急车道 v3版本
bool vehicle_on_nondriveway_v3(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint repeat_times = 1,
    gdouble vehicle_conf_thres = 0.5,
    gint duration = 0)
{
    gboolean ret = FALSE;
    int times = std::max(repeat_times, duration * 25);
    static cache::lru_cache<std::string, int> vehicle_on_nondriveway_lru(500);
    if (obj_meta->class_id == PGIE_CLASS_ID_VEHICLE && obj_meta->confidence > vehicle_conf_thres)
    {
        std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
        for (auto polygon : polygons)
        {
            if (point_in_polygon(point, polygon))
            {
                std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
                if (!vehicle_on_nondriveway_lru.exists(sensor_obj))
                {
                    vehicle_on_nondriveway_lru.put(sensor_obj, 1);
                }
                else
                {
                    vehicle_on_nondriveway_lru.put(sensor_obj, vehicle_on_nondriveway_lru.get(sensor_obj) + 1);
                }
                // 第 repeat_times 次出现，进行告警
                if (vehicle_on_nondriveway_lru.get(sensor_obj) == times)
                {
                    ret = TRUE;
                    return ret;
                }
            }
        }
    }
    return ret;
}

// 9. 非机动车闯入
bool nonvehicle_on_driveway(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint times,
    gdouble thres)
{

    gboolean ret = FALSE;
    static cache::lru_cache<std::string, int> nonvehicle_on_driveway_lru(500);
    //如果不是非机动车类型，直接return
    if (obj_meta->class_id != PGIE_CLASS_ID_OTHER_VEHICLE)
    {
        return ret;
    }
    // 过滤低置信度目标
    if (obj_meta->confidence < thres)
    {
        return ret;
    }

    std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
    for (auto polygon : polygons)
    {
        if (point_in_polygon(point, polygon))
        {
            std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
            if (!nonvehicle_on_driveway_lru.exists(sensor_obj))
            {
                nonvehicle_on_driveway_lru.put(sensor_obj, 1);
            }
            else
            {
                nonvehicle_on_driveway_lru.put(sensor_obj, nonvehicle_on_driveway_lru.get(sensor_obj) + 1);
            }
            // 第 times 次出现，进行告警
            if (nonvehicle_on_driveway_lru.get(sensor_obj) == times)
            {
                ret = TRUE;
                return ret;
            }
        }
    }
    return ret;
}

// 9. 非机动车闯入 v3版
bool nonvehicle_on_driveway_v3(
    NvDsFrameMeta *frame_meta,
    NvDsObjectMeta *obj_meta,
    std::vector<std::vector<std::pair<double, double>>> &polygons,
    gint repeat_times = 1,
    gdouble other_vehicle_conf_thres = 0.5,
    gint duration = 0)
{
    gboolean ret = FALSE;
    int times = std::max(repeat_times, duration * 25);
    static cache::lru_cache<std::string, int> nonvehicle_on_driveway_lru(500);
    if (obj_meta->class_id == PGIE_CLASS_ID_OTHER_VEHICLE && obj_meta->confidence > other_vehicle_conf_thres)
    {

        std::pair<float, float> point = get_bottom_center_point(frame_meta, obj_meta);
        for (auto polygon : polygons)
        {
            if (point_in_polygon(point, polygon))
            {
                std::string sensor_obj = std::to_string(frame_meta->source_id) + '_' + std::to_string(obj_meta->object_id);
                if (!nonvehicle_on_driveway_lru.exists(sensor_obj))
                {
                    nonvehicle_on_driveway_lru.put(sensor_obj, 1);
                }
                else
                {
                    nonvehicle_on_driveway_lru.put(sensor_obj, nonvehicle_on_driveway_lru.get(sensor_obj) + 1);
                }
                // 第 times 次出现，进行告警
                if (nonvehicle_on_driveway_lru.get(sensor_obj) == times)
                {
                    ret = TRUE;
                    return ret;
                }
            }
        }
    }
    return ret;
}

// 告警事件触发时间
bool in_time_rule(
    gint *time_rule)
{
    struct timespec ts
    {
    };
    clock_gettime(CLOCK_REALTIME, &ts);
    time_t now_timestamp = ts.tv_sec;
    tm *tm_t = localtime(&now_timestamp);
    // std::cout << "year:" << tm_t->tm_year + 1900 << " month:" << tm_t->tm_mon + 1 << " day:" << tm_t->tm_mday
    //           << " hour:" << tm_t->tm_hour << " minute:" << tm_t->tm_min << " second:" << tm_t->tm_sec << std::endl;
    bool is_after_begin = tm_t->tm_hour > time_rule[0] || (tm_t->tm_hour == time_rule[0] && tm_t->tm_min >= time_rule[1]);
    bool is_before_end = tm_t->tm_hour < time_rule[2] || (tm_t->tm_hour == time_rule[2] && tm_t->tm_min < time_rule[3]);
    return is_after_begin && is_before_end;
}