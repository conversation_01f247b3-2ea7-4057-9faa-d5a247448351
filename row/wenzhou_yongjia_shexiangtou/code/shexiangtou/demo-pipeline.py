'''
demo图像质量检测推理演示整体pipeline，分为花屏（mosaic）、模糊（blur）、遮挡（shield）依次进行检测
统一判定为abnormal
'''
import os
import cv2
from ImageAnomalyDetection import Inference
from show_img_shield import one_img_show

#TEST_DIR = './test_images'
TEST_DIR = './/val_data//mosaic'
filelist = os.listdir(TEST_DIR)
print(filelist)
Blur = Inference.BlurInference()
Mosaic = Inference.MosaicInference()

# Image anomaly detection pipeline
for i in range(0, len(filelist)):
    path = os.path.join(TEST_DIR, filelist[i])
    img = cv2.imread(path)
    result1 = Mosaic.mosaic_det_v1(img)
    if result1["is_mosaic"]:
        print(f'opencv mat mosaic detect result of {filelist[i]} is abnormal')
    else:
        result2 = Blur.blur_det_v1(img)
        if result2["is_blur"]:
            print(f'opencv mat blur detect result of {filelist[i]} is abnormal')
        else:
            _, result3 = one_img_show(img)
            if result3["shield_type"] == "shield":
                print(f'opencv mat shield detect result of {filelist[i]} is abnormal')


