python3 -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple --upgrade pip
pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
echo "===================== config pip ======================="
pip3 install -r requirements.txt
echo "============= python packages are installed ============"
python3 setup.py install
echo "====== Image Anomaly Detection algo lib are installed ====="
