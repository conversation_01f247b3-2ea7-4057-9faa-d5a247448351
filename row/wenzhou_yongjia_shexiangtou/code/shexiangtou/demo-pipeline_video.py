'''
demo图像质量检测推理演示整体pipeline，分为花屏（mosaic）、模糊（blur）、遮挡（shield）依次进行检测
统一判定为abnormal
'''
import os
import cv2
from ImageAnomalyDetection import Inference
from show_img_shield import one_img_show

# TEST_DIR = './test_images'
# filelist = os.listdir(TEST_DIR)
# print(filelist)
Blur = Inference.BlurInference()
Mosaic = Inference.MosaicInference()

# Image anomaly detection pipeline
def ab_detect(img):
    r_s =dict()
    result1 = Mosaic.mosaic_det_v1(img)
    if result1["is_mosaic"]:
        print(f'opencv mat mosaic detect result of  is abnormal')
        r_s=result1
    else:
        result2 = Blur.blur_det_v1(img)
        if result2["is_blur"]:
            print(f'opencv mat blur detect result of  is abnormal')
            r_s = result2
        else:
            _, result3 = one_img_show(img)
            if result3["shield_type"] == "shield":
                print(f'opencv mat shield detect result of is abnormal')
                r_s = result3
    return r_s

cap = cv2.VideoCapture('./test_images/mohu.mp4')
import cvzone
while(cap.isOpened()):
    ret, img = cap.read()
    r_s = ab_detect(img)

    cvzone.putTextRect(img, f'CTYUN : '+str(r_s), (30, 40), scale=2, thickness=2,
                       offset=20,
                       colorR=(0, 200, 0))
    cv2.imshow('frame',img)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()

    
