'''
demo图像质量检测推理演示，分为花屏检测（mosaic）、模糊检测（blur）、遮挡检测（shield）
'''
import os
import cv2
from ImageAnomalyDetection import Inference
from show_img_shield import one_img_show

TEST_DIR = './example-images/'
filelist = os.listdir(TEST_DIR)

Blur = Inference.BlurInference()
Mosaic = Inference.MosaicInference()
# I. blur detection, take opencv mat for example
for i in range(0, len(filelist)):
    path = os.path.join(TEST_DIR, filelist[i])
    img = cv2.imread(path)
    result = Blur.blur_det_v1(img)
    print(f'opencv mat blur detect result of {filelist[i]} is {result}')

# II. mosaic detection, take opencv mat for example
for i in range(0, len(filelist)):
    path = os.path.join(TEST_DIR, filelist[i])
    img = cv2.imread(path)
    result = Mosaic.mosaic_det_v1(img)
    print(f'opencv mat mosaic detect result of {filelist[i]} is {result}')

# III. shield detection
for i in range(0, len(filelist)):
    path = os.path.join(TEST_DIR, filelist[i])
    img = cv2.imread(path)
    #输入图片给出是否为遮挡的判断，输出绘制后图片，图片遮挡，疑似和正常三种类型之一，和遮挡比率
    _, result = one_img_show(img)
    print(f'opencv mat shield detect result of {filelist[i]} is {result}')
