'''
fastapi demo as server
'''
from typing import Union
from fastapi import FastAPI
import uvicorn
from typing import Optional
from pydantic import BaseModel
import os
import sys
import time

from ImageAnomalyDetection import Inference
blur_det = Inference.BlurInference()
mosaic_det = Inference.MosaicInference()

class PostData(BaseModel):
    ImageData: Optional[str]

class BlurReturnResult(BaseModel):
    is_blur: Optional[bool]
    score: Optional[float]
    status: Optional[int]
    message: Optional[str]

class MosaicReturnResult(BaseModel):
    is_mosaic: Optional[bool]
    score: Optional[float] 
    status: Optional[int]
    message: Optional[str]

class AllReturnResult(BaseModel):
    blur: Optional[dict]
    mosaic: Optional[dict]
    status: Optional[int]
    message: Optional[str]    

app = FastAPI(title = "Image Blur Detection Service Demo")

@app.get("/")
def read_root():
    return {"Hello": "Image Blur Detection!"}

@app.post('/imageBlurDetect/', response_model = BlurReturnResult)
def imageBlurDetect(item: PostData):
    timeTraceStart = time.time()
    input_image = str(item.ImageData)
    result = blur_det.blur_det_v1(input_image)
    # result = blur_det.blur_det_v0(input_image)
    ReturnData = BlurReturnResult()
    if "is_blur" in result.keys():
        ReturnData.status = 200
        ReturnData.message = 'detect ok'
    ReturnData.is_blur = result["is_blur"]
    ReturnData.score = result["score"]
    timeTraceEnd = time.time()
    print("checkImagePost time use: %.3f s" % (timeTraceEnd - timeTraceStart))
    return ReturnData

@app.post('/imageMosaicDetect/', response_model = MosaicReturnResult)
def imageBlurDetect(item: PostData):
    timeTraceStart = time.time()
    input_image = str(item.ImageData)
    result = mosaic_det.mosaic_det_v1(input_image)
    ReturnData = MosaicReturnResult()
    if "is_mosaic" in result.keys():
        ReturnData.status = 200
        ReturnData.message = 'detect ok'
    ReturnData.is_mosaic = result["is_mosaic"]
    ReturnData.score = result["score"]
    timeTraceEnd = time.time()
    print("checkImagePost time use: %.3f s" % (timeTraceEnd - timeTraceStart))
    return ReturnData

@app.post('/imageAllDetect/', response_model = AllReturnResult)
def imageBlurDetect(item: PostData):
    timeTraceStart = time.time()
    input_image = str(item.ImageData)
    result = Inference.det_all(input_image)
    ReturnData = AllReturnResult()
    if "is_blur" in result["blur"].keys() and "is_mosaic" in result["mosaic"].keys():
        ReturnData.status = 200
        ReturnData.message = 'detect ok'
    ReturnData.blur = result["blur"]
    ReturnData.mosaic = {"is_mosaic": result["mosaic"]["is_mosaic"], "score": float(result["mosaic"]["score"])}
    timeTraceEnd = time.time()
    print("checkImagePost time use: %.3f s" % (timeTraceEnd - timeTraceStart))
    return ReturnData

if __name__ == "__main__":
    uvicorn.run("ImageAnomalyDetectionService:app", host="0.0.0.0", port=5678)
    # uvicorn.run("ImageAnomalyDetectionService:app", host="0.0.0.0", port=5678, reload = True)
