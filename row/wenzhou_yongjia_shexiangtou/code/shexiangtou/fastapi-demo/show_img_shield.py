import cv2
import pickle
import cvzone
import numpy as np
import time
import cv2
import pickle

def one_img_show(img):
    def checkSpace(img):
        def getpos_relyimgw(h, w):
            box_h = int((h - 160) / 2)
            if (w < 2000):
                box_w = int((w - 5) / 3)
                for i in range(3):
                    posList.append(((5 + box_w * i, 100), (5 + box_w * (i + 1), 100 + box_h)))
                for i in range(3):
                    posList.append(((5 + box_w * i, 100 + box_h + 30), (5 + box_w * (i + 1), 100 + 2 * box_h + 30)))
            if (w > 2000):
                box_w = int((w - 5) / 4)
                for i in range(4):
                    posList.append(((5 + box_w * i, 100), (5 + box_w * (i + 1), 100 + box_h)))
                for i in range(4):
                    posList.append(((5 + box_w * i, 100 + box_h + 30), (5 + box_w * (i + 1), 100 + 2 * box_h + 30)))

            return posList

        h, w, _ = img.shape
        posList = []
        posList = getpos_relyimgw(h, w)
        spaces = 0

        # 图像信息阈值参数
        val1 = 34
        val2 = 5
        val3 = 1100

        imgGray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        imgBlur = cv2.GaussianBlur(imgGray, (3, 3), 1)

        if val1 % 2 == 0:
            val1 += 1

        imgThres = cv2.adaptiveThreshold(imgBlur, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY_INV, val1, val2)

        imgThres = cv2.medianBlur(imgThres, 5)
        kernel = np.ones((3, 3), np.uint8)

        imgThres = cv2.dilate(imgThres, kernel, iterations=1)
        cropList = []
        for pos in posList:
            (x1, y1), (x2, y2) = pos[0], pos[1]

            imgCrop = imgThres[y1:y2, x1:x2]
            count = cv2.countNonZero(imgCrop)

            if count < val3:
                color = (0, 200, 0)
                thics = 5
                spaces += 1
            else:
                color = (0, 0, 200)
                thics = 2
            cv2.rectangle(img, (x1, y1), (x2, y2), color, thics)
            cv2.putText(img, str(count), (x1, y1 + 33), cv2.FONT_HERSHEY_PLAIN, 2, color, 1)

        s_p = round(spaces / len(posList), 2)
        cvzone.putTextRect(img, f'CTYUN Shield:{s_p} {spaces}/{len(posList)} ', (30, 40), scale=2, thickness=2,
                           offset=20,
                           colorR=(0, 200, 0))
        return img, spaces, s_p
    version = 'v1'
    img_type='normal'
    try:

                if version == 'v1':
                    img,space,s_p = checkSpace(img)

                if (space==1):

                    # cv2.imwrite(fj_path + img_path.split("\\")[-1], img)
                    img_type='maybe'
                if(space==0):

                    # cv2.imwrite(normal_path+img_path.split("\\")[-1],img)
                    img_type='normal'
                if(space>1 and space<=6):

                    # cv2.imwrite(shield_path + img_path.split("\\")[-1], img)
                    img_type='shield'
    except Exception as e:
                print(e)
    return img,img_type,s_p



if __name__ == '__main__':
    # import glob
    # #输入判断的文件夹
    # path = './/test//*'

    # shield_path = './/test3/shield//'
    # fj_path = './/test3//fj_path//'
    # normal_path = './/test3//normal//'
    # import os
    # os.makedirs(shield_path, exist_ok=True)
    # os.makedirs(fj_path, exist_ok=True)
    # os.makedirs(normal_path, exist_ok=True)

    # for i,img_path in enumerate(glob.glob(path)):
    #     img = cv2.imread(img_path)
    #     #输入图片给出是否为遮挡的判断，输出绘制后图片，图片遮挡，疑似和正常三种类型之一，和遮挡比率
    #     img_result,img_type,shile_ratio = one_img_show(img)
    #     print("current index", i, img_path,"  ",img_type," shiled ratio",shile_ratio)

    img = cv2.imread('zhedang2.png')
    #输入图片给出是否为遮挡的判断，输出绘制后图片，图片遮挡，疑似和正常三种类型之一，和遮挡比率
    img_result,img_type,shile_ratio = one_img_show(img)
    print("current index", 0, 'zhedang2.jpg',"  ",img_type," shiled ratio",shile_ratio)    