'''
fastapi demo as client
'''
import json
import base64
import requests
import os
from show_img_shield import one_img_show
import cv2

def encode_base64_string(image_path):
    with open(image_path, "rb") as f:
        encoded_string = str(base64.urlsafe_b64encode(f.read()), "utf-8")
    return encoded_string

# target_url = 'http://0.0.0.0:5678/imageBlurDetect/'
# target_url = 'http://0.0.0.0:5678/imageMosaicDetect/'
target_url = 'http://0.0.0.0:5678/imageAllDetect/'
headers = {"content-type": "application/json"}

TEST_DIR0 = '/data/lvbo/exchange/tykj_video/save_one'

SAVE_DIR1 = './blur-good-case'
SAVE_DIR2 = './mosaic-good-case'
SAVE_DIR3 = './shield-good-case'

cmd = f'rm -rf {SAVE_DIR1}'
os.system(cmd)

cmd = f'rm -rf {SAVE_DIR2}'
os.system(cmd)

cmd = f'rm -rf {SAVE_DIR3}'
os.system(cmd)

os.makedirs(SAVE_DIR1, exist_ok = True)
os.makedirs(SAVE_DIR2, exist_ok = True)
os.makedirs(SAVE_DIR3, exist_ok = True)

filelist = os.listdir(TEST_DIR0)
for i in range(0, len(filelist)):
    path = os.path.join(TEST_DIR0, filelist[i])
    # print(f'path is {path}')
    image_base64_string = encode_base64_string(path)
    params = {
            "ImageData": image_base64_string
        }
    json_data = json.dumps(params)
    resp = requests.post(target_url, data=json_data, headers=headers, timeout=300)
    if resp.status_code != 200:
        print(resp.status_code)
        print("Send fail..")
    else:
        resp_data = json.loads(resp.text)
        # print(resp_data)
    if resp_data["mosaic"]["is_mosaic"]:
        det_path = os.path.join(SAVE_DIR2, filelist[i])
        cmd = f'cp {path} {det_path}'
        print(cmd)
        os.system(cmd)
    else:
        if resp_data["blur"]["is_blur"]:
            det_path = os.path.join(SAVE_DIR1, filelist[i])
            cmd = f'cp {path} {det_path}'
            print(cmd)
            os.system(cmd)
        else:
            img = cv2.imread(path)
            #输入图片给出是否为遮挡的判断，输出绘制后图片，图片遮挡，疑似和正常三种类型之一，和遮挡比率
            _, img_type, shile_ratio = one_img_show(img)
            if img_type == 'shield':
                det_path = os.path.join(SAVE_DIR3, filelist[i])
                cmd = f'cp {path} {det_path}'
                print(cmd)
                os.system(cmd)
