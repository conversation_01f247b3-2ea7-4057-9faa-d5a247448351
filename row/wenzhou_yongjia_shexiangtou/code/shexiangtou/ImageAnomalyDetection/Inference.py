'''
single image blur detect algo
add some boundary conditions
'''
# coding: utf-8
import os
import sys
import json
import math
import time
import numpy as np
from numba import jit
import cv2
import torch

from ImageAnomalyDetection.SingleImageBlurDetection import BlurDetection
from ImageAnomalyDetection.SingleImageMosaicDetection import MosaicDetection

def save_json(save_path, data):
    assert save_path.split('.')[-1] == 'json'
    with open(save_path,'w') as file:
        json.dump(data,file)

class BlurInference(BlurDetection):

    def __init__(self, config_path = './config/AlgoConfig.json') -> None:
        super(BlurInference, self).__init__()
        data = self.load_json(config_path)
        self.blur = data['algo']['blur']
        self.blur_threshold = data['thres']['blur']
        print(f'load config .. {data}')

    def blur_det_v1(self, img):
        '''
        防止局部模糊出现误判，采用切片检测机制，当整体检测为blur后，进一步将原图切分为上下左右4块，再做模糊检测
        如果4块切分图均为blur，证明整体为模糊，输出为blur
        反之，为清晰（包含局部模糊）
        '''
        assert img is not None, 'the input image is None!!!'
        if type(img) != np.ndarray:
            img = self.base64_to_image(img) # base64 to ndarray
        result_base = self.infer(img)
        if result_base["is_blur"]:
            h, w, _ = img.shape
            img_slide1 = img[0:h//2,0:w//2]
            img_slide2 = img[0:h//2,w//2:w]
            img_slide3 = img[h//2:h,0:w//2]
            img_slide4 = img[h//2:h,w//2:w]
            result1 = self.infer(img_slide1)
            result2 = self.infer(img_slide2)
            result3 = self.infer(img_slide3)
            result4 = self.infer(img_slide4)
            if result1["is_blur"] and result2["is_blur"] and result3["is_blur"] and result4["is_blur"]:
                result_base["is_blur"] = True
                # return {"is_blur": True, "score": result_base["score"]}
            else:
                result_base["is_blur"] = False
                result_base["score"] = 0.25 * (result1["score"] + result2["score"] + result3["score"] + result4["score"])
                # return {"is_blur": False, "score": 0.25 * (result1["score"] + result2["score"] + result3["score"] + result4["score"])}
            if result_base["score"] >= self.blur_threshold:
                result_base["is_blur"] = False
            else:
                result_base["is_blur"] = True
        return result_base

    def blur_det_v0(self, img):
        '''
        xxxxxx
        '''
        assert img is not None, 'the input image is None!!!'
        if type(img) != np.ndarray:
            img = self.base64_to_image(img) # base64 to ndarray
        result_base = self.infer(img)
        if result_base["score"] >= self.blur_threshold:
            result_base["is_blur"] = False
        return result_base

    def load_json(self, file_path):
        assert file_path.split('.')[-1] == 'json'
        with open(file_path,'r') as file:
            data = json.load(file)
        return data

class MosaicInference(MosaicDetection):

    def __init__(self, config_path = './config/AlgoConfig.json') -> None:
        super(MosaicInference, self).__init__()
        data = self.load_json(config_path)
        self.mosaic = data['algo']['mosaic']
        self.mosaic_threshold = data['thres']['mosaic']
        print(f'load config .. {data}')
    
    # @staticmethod
    def mosaic_det_v1(self, img):
        '''
        is_mosaic:
        score: float
        '''
        assert img is not None, 'the input image is None!!!'
        if type(img) != np.ndarray:
            img = self.base64_to_image(img) # base64 to ndarray
        result = self.infer(img)
        return result

    def load_json(self, file_path):
        assert file_path.split('.')[-1] == 'json'
        with open(file_path,'r') as file:
            data = json.load(file)
        return data

Blur = BlurInference()
Mosaic = MosaicInference()

def det_all(img):
    result = {}
    blur_result = Blur.blur_det_v1(img)
    mosaic_result = Mosaic.mosaic_det_v1(img)
    result["blur"] = blur_result
    result["mosaic"] = mosaic_result
    return result

if __name__ == '__main__':
    Infer = BlurInference(config_path = './config/AlgoConfig.json')
    test_dir = './save_one_result/blur-good-case'
    TEST_DIR = test_dir
    filelist = os.listdir(TEST_DIR)
    for i in range(0, len(filelist)):
        path = os.path.join(TEST_DIR, filelist[i])
        img = cv2.imread(path)
        # img = Infer.image_to_base64(img)
        result = Infer.blur_det_v1(img)
        # result = det_all(img)
        print(f'path is {path}')
        print(result)
        print("==" * 15)
        # print(f'wrong result is {path}')
        # print(f'metrics score is {Infer.blur_metrics_compute(img)}')
