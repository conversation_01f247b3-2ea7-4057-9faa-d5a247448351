import numpy as  np
import cv2
import time
import torch
import torchvision
import onnxruntime
import math
import torch.nn.functional as F
import torchvision.transforms as T
import base64
import json

def classify_transforms(size=448):
    class CenterCrop:
        # YOLOv5 CenterCrop class for image preprocessing, i.e. T.Compose([CenterCrop(size), ToTensor()])
        def __init__(self, size=640):
            super().__init__()
            self.h, self.w = (size, size) if isinstance(size, int) else size

        def __call__(self, im):  # im = np.array HWC
            imh, imw = im.shape[:2]
            m = min(imh, imw)  # min dimension
            top, left = (imh - m) // 2, (imw - m) // 2
            return cv2.resize(im[top:top + m, left:left + m], (self.w, self.h), interpolation=cv2.INTER_LINEAR)

    class C_Resize:
            # YOLOv5 CenterCrop class for image preprocessing, i.e. T.Compose([CenterCrop(size), ToTensor()])
            def __init__(self, size=448):
                super().__init__()
                self.h, self.w = (size, size) if isinstance(size, int) else size

            def __call__(self, im):  # im = np.array HWC
                return cv2.resize(im, (self.w, self.h), interpolation=cv2.INTER_LINEAR)

    class ToTensor:
        # YOLOv5 ToTensor class for image preprocessing, i.e. T.Compose([LetterBox(size), ToTensor()])
        def __init__(self, half=False):
            super().__init__()
            self.half = half

        def __call__(self, im):  # im = np.array HWC in BGR order
            im = np.ascontiguousarray(im.transpose((2, 0, 1))[::-1])  # HWC to CHW -> BGR to RGB -> contiguous
            im = torch.from_numpy(im)  # to torch
            im = im.float()  # uint8 to fp16/32
            im /= 255.0  # 0-255 to 0.0-1.0
            return im
    # Transforms to apply if albumentations not installed
    assert isinstance(size, int), f'ERROR: classify_transforms size {size} must be integer, not (list, tuple)'
    IMAGENET_MEAN = 0.485, 0.456, 0.406  # RGB mean
    IMAGENET_STD = 0.229, 0.224, 0.225
    return T.Compose([CenterCrop(size), ToTensor(), T.Normalize(IMAGENET_MEAN, IMAGENET_STD)])

def preprocess(im0,img_size=448):
    transforms = classify_transforms(img_size)
    im = transforms(im0)
    if len(im.shape) == 3:
        im = im[None]  # expand for batch dim
    im = im.cpu().numpy()
    return im

class BlurDetection():
    def __init__(self, config_path = './config/AlgoConfig.json', is_fp16=False):
        """
        :param onnx_path:
        """
        #onnx.checker.check_model(onnx_path)
        data = self.load_json(config_path)
        self.onnx_path = data["model"]["blur_model_path"]
        self.onnx_session = onnxruntime.InferenceSession(self.onnx_path, providers=['CUDAExecutionProvider'])
        self.input_name = self.get_input_name(self.onnx_session)
        self.output_name = self.get_output_name(self.onnx_session)
        self.is_fp16  = is_fp16

    def get_output_name(self, onnx_session):
        """
        output_name = onnx_session.get_outputs()[0].name
        :param onnx_session:
        :return:
        """
        output_name = []
        for node in onnx_session.get_outputs():
            output_name.append(node.name)
        return output_name

    def get_input_name(self, onnx_session):
        """
        input_name = onnx_session.get_inputs()[0].name
        :param onnx_session:
        :return:
        """
        input_name = []
        for node in onnx_session.get_inputs():
            input_name.append(node.name)
        return input_name

    def get_input_feed(self, input_name, image_numpy):
        """
        input_feed={self.input_name: image_numpy}
        :param input_name:
        :param image_numpy:
        :return:
        """
        input_feed = {}
        for name in input_name:
            input_feed[name] = image_numpy
        return input_feed

    def forward(self, image_numpy):
        '''
        # image_numpy = image.transpose(2, 0, 1)
        # image_numpy = image_numpy[np.newaxis, :]
        # onnx_session.run([output_name], {input_name: x})
        # :param image_numpy:
        # :return:
        '''
       # scores, boxes = self.onnx_session.run(self.output_name, input_feed={self.input_name: iimage_numpy})
        input_feed = self.get_input_feed(self.input_name, image_numpy)
        results = self.onnx_session.run(self.output_name, input_feed=input_feed)
        return results

    def infer(self, img0, img_size = 448):
        """_summary_

        Args:
            img0 (_type_): _description_
            img_size (int, optional): _description_. Defaults to 448.

        Returns:
            result: (dict): 
        """        
        im = preprocess(img0, img_size)
        # print("the origin shape", img0.shape, "result shape", im.shape)
        im = im.astype(np.float16) if self.is_fp16 else im
        results = self.forward(im)[0]
        results = results.astype(np.float32)
        prob = F.softmax(torch.from_numpy(results), dim=1)  # probabilities
        top1 = prob[0].argsort(0, descending=True)[:1].tolist()  # top 1 indices 0 -> blur; 1 -> normal
        if top1[0] == 0:
            return {"is_blur": True, "score": float(1 - prob.numpy()[0][0])}
        else:
            return {"is_blur": False, "score": float(1 - prob.numpy()[0][0])}

    def base64_to_image(self, base64_code):
        """
        将base64编码解析成opencv可用图片
        base64_code: base64编码后数据
        Returns: cv2图像，numpy.ndarray
        """
        # base64解码
        # img_data = base64.b64decode(base64_code)
        img_data = base64.urlsafe_b64decode(base64_code)
        # 转换为np数组
        img_array = np.fromstring(img_data, np.uint8)
        # 转换成opencv可用格式
        img = cv2.imdecode(img_array, cv2.COLOR_RGB2BGR) 
        return img
    def load_json(self, file_path):
        assert file_path.split('.')[-1] == 'json'
        with open(file_path,'r') as file:
            data = json.load(file)
        return data

if __name__ =='__main__':
    import os
    import time
    onnx_run = BlurDetection(config_path = './config/AlgoConfig.json', is_fp16=False)

    TEST_DIR = './save_one_result/blur-good-case'
    filelist = os.listdir(TEST_DIR)
    for i in range(0, len(filelist)):        
        t1 = time.time()
        path = os.path.join(TEST_DIR, filelist[i])
        img0 = cv2.imread(path)
        result = onnx_run.infer(img0)
        print(f'path is {path}')
        print(f'result is {result}')
        t2 = time.time()
        print(f'process time is {(t2 - t1)*1e3} ms')
