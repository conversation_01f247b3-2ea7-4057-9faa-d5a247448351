import logging as log
import os
import sys
import time
import glob
import cv2
import numpy as np
import pybase64 as base64
import tritonclient.http as httpclient
from PIL import Image
import gevent
import gevent.ssl
import yaml
import logging
from scipy.special import softmax
import os
import fleep
from teleexception import StatusException, HTTPStatus
from io import BytesIO
from icecream import ic
import torch
import torch.nn.functional as F
import torchvision.transforms as T

logger = logging.getLogger(__name__)

CONFIG_YAML_PATH = "../../config.yaml"
LOCK_PATH = f"/tmp/init_logger"

with open(
    os.path.join(os.path.dirname(__file__), CONFIG_YAML_PATH), "r", encoding="utf-8"
) as f:
    configs = yaml.load(f, Loader=yaml.FullLoader)
    if not os.path.exists(LOCK_PATH) or not os.path.isdir(LOCK_PATH):
        os.makedirs(LOCK_PATH, exist_ok=True)
        logger.info(f"Init, {configs}")

action_configs = configs["action"]  # ACTION
port_configs = configs["ports"]  # 端口配置
models_configs = configs["models"]  # 模型参数配置
process_configs = configs["process"]  # 运行参数配置
params_configs = configs["params"]  # 系统参数配置

# 画面异常检测 端口
TRITON_URL = os.environ.get(
    "TRITON_URL", "localhost:{}".format(port_configs["TRITON_PORT"])
)
# 面积阈值 配置
CONFIG_AREA_THRESH = float(
    os.environ.get("AREA_THRESH", process_configs["AREA_THRESH"])
)
# 模糊阈值 配置
CONFIG_BLUR_THRESH = float(
    os.environ.get("BLUR_THRESH", process_configs["BLUR_THRESH"])
)
# 花屏检测 模型配置
mosaic_detect_configs = models_configs["mosaic_detect"]
# 模糊检测 模型配置
blur_detect_configs = models_configs["blur_detect"]


class DetectClient:
    def __init__(self, url, model_config):
        self.model_config = model_config
        try:
            if self.model_config["ssl"]:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url,
                    verbose=self.model_config["verbose"],
                    ssl=True,
                    ssl_context_factory=gevent.ssl._create_unverified_context,
                    insecure=True,
                )
            else:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url, verbose=self.model_config["verbose"]
                )
        except Exception as e:
            logger.error("channel creation failed: " + str(e))
            sys.exit(1)
        if self.model_config["http_headers"] is not None:
            self.headers = {
                l.split(":")[0]: l.split(":")[1]
                for l in self.model_config["http_headers"]
            }
        else:
            self.headers = None
        self.model_name = self.model_config["model_name"]
        self.request_compression_algorithm = self.model_config[
            "request_compression_algorithm"
        ]
        self.response_compression_algorithm = self.model_config[
            "response_compression_algorithm"
        ]

    def run(self, input_data):
        inputs = [
            httpclient.InferInput(
                input_info["name"], input_data.shape, input_info["dtype"]
            )
            for input_info in self.model_config["input"]
        ]
        inputs[0].set_data_from_numpy(input_data, binary_data=True)

        outputs = [
            httpclient.InferRequestedOutput(output_info["name"], binary_data=True)
            for output_info in self.model_config["output"]
        ]
        query_params = {"test_1": 1, "test_2": 2}
        results = self.triton_client.async_infer(
            self.model_name,
            inputs,
            outputs=outputs,
            query_params=query_params,
            headers=self.headers,
            request_compression_algorithm=self.request_compression_algorithm,
            response_compression_algorithm=self.response_compression_algorithm,
        )
        return results


# 花屏检测 模型客户端
mosaic_detect_client = DetectClient(TRITON_URL, mosaic_detect_configs)
# 模糊检测 模型客户端
blur_detect_client = DetectClient(TRITON_URL, blur_detect_configs)


class BlockDetect:
    def __init__(self, process_configs, reqid=None):
        self.reqid = reqid
        self.process_configs = process_configs
        self.save_rule = params_configs["SAVE_RULE"]  # 默认保存规则 all

    def classify_transforms(self, size=448):
        class CenterCrop:
            # YOLOv5 CenterCrop class for image preprocessing, i.e. T.Compose([CenterCrop(size), ToTensor()])
            def __init__(self, size=640):
                super().__init__()
                self.h, self.w = (size, size) if isinstance(size, int) else size

            def __call__(self, im):  # im = np.array HWC
                imh, imw = im.shape[:2]
                m = min(imh, imw)  # min dimension
                top, left = (imh - m) // 2, (imw - m) // 2
                return cv2.resize(
                    im[top : top + m, left : left + m],
                    (self.w, self.h),
                    interpolation=cv2.INTER_LINEAR,
                )

        class C_Resize:
            # YOLOv5 CenterCrop class for image preprocessing, i.e. T.Compose([CenterCrop(size), ToTensor()])
            def __init__(self, size=448):
                super().__init__()
                self.h, self.w = (size, size) if isinstance(size, int) else size

            def __call__(self, im):  # im = np.array HWC
                return cv2.resize(im, (self.w, self.h), interpolation=cv2.INTER_LINEAR)

        class ToTensor:
            # YOLOv5 ToTensor class for image preprocessing, i.e. T.Compose([LetterBox(size), ToTensor()])
            def __init__(self, half=False):
                super().__init__()
                self.half = half

            def __call__(self, im):  # im = np.array HWC in BGR order
                # HWC to CHW -> BGR to RGB -> contiguous
                im = np.ascontiguousarray(im.transpose((2, 0, 1))[::-1])
                im = torch.from_numpy(im)  # to torch
                im = im.float()  # uint8 to fp16/32
                im /= 255.0  # 0-255 to 0.0-1.0
                return im

        # Transforms to apply if albumentations not installed
        assert isinstance(
            size, int
        ), f"ERROR: classify_transforms size {size} must be integer, not (list, tuple)"
        IMAGENET_MEAN = 0.485, 0.456, 0.406  # RGB mean
        IMAGENET_STD = 0.229, 0.224, 0.225

        return T.Compose(
            [CenterCrop(size), ToTensor(), T.Normalize(IMAGENET_MEAN, IMAGENET_STD)]
        )

    # 图片预处理
    def preprocess(self, im0, img_size=448):
        transforms = self.classify_transforms(img_size)
        im = transforms(im0)
        if len(im.shape) == 3:
            im = im[None]  # expand for batch dim
        im = im.cpu().numpy()
        return im

    # 花屏检测 推理模块
    def mosaic_infer(self, img0, img_size=448):
        # 预处理
        im = self.preprocess(img0, img_size)

        # 模型推理
        mosaic_detect = mosaic_detect_client.run(im)
        mosaic_detect_results = mosaic_detect.get_result()
        mosaic_output = mosaic_detect_results.as_numpy("output0")

        # 后处理
        prob = F.softmax(torch.from_numpy(mosaic_output), dim=1)  # probabilities
        # top 1 indices 0 -> mosaic; 1 -> normal
        top1 = prob[0].argsort(0, descending=True)[:1].tolist()

        is_mosaic = True if top1[0] == 0 else False  # 是否花屏
        score = float(1 - prob.numpy()[0][0])  # 无花屏分数

        return is_mosaic, score

    # 花屏检测 处理
    def mosaic_process(self, img_mat):
        # 整张图片推理
        base_mosaic, base_score = self.mosaic_infer(img_mat)  # BGR

        if base_mosaic:  # True
            h, w, _ = img_mat.shape
            img_slide1 = img_mat[0 : h // 2, 0 : w // 2]
            img_slide2 = img_mat[0 : h // 2, w // 2 : w]
            img_slide3 = img_mat[h // 2 : h, 0 : w // 2]
            img_slide4 = img_mat[h // 2 : h, w // 2 : w]
            is_mosaic_1, score_1 = self.mosaic_infer(img_slide1)
            is_mosaic_2, score_2 = self.mosaic_infer(img_slide2)
            is_mosaic_3, score_3 = self.mosaic_infer(img_slide3)
            is_mosaic_4, score_4 = self.mosaic_infer(img_slide4)
            # 切分后都为 True
            if is_mosaic_1 and is_mosaic_2 and is_mosaic_3 and is_mosaic_4:
                return True, base_score
            else:  # 切分后存在 False
                fix_score = 0.25 * (score_1 + score_2 + score_3 + score_4)
                return False, fix_score
        else:  # False
            return False, base_score

    # 模糊检测 推理模块
    def blur_infer(self, img0, img_size=448):
        # 预处理
        im = self.preprocess(img0, img_size)

        # 模型推理
        blur_detect = blur_detect_client.run(im)
        blur_detect_results = blur_detect.get_result()
        blur_output = blur_detect_results.as_numpy("output0")

        # 后处理
        prob = F.softmax(torch.from_numpy(blur_output), dim=1)  # probabilities
        # top 1 indices 0 -> blur; 1 -> normal
        top1 = prob[0].argsort(0, descending=True)[:1].tolist()

        is_blur = True if top1[0] == 0 else False  # 是否模糊
        score = float(1 - prob.numpy()[0][0])  # 无模糊分数

        return is_blur, score

    # 模糊检测 处理
    def blur_process(self, img_mat):
        # 整张图片推理
        base_blur, base_score = self.blur_infer(img_mat)  # BGR

        if base_blur:  # True
            h, w, _ = img_mat.shape
            img_slide1 = img_mat[0 : h // 2, 0 : w // 2]
            img_slide2 = img_mat[0 : h // 2, w // 2 : w]
            img_slide3 = img_mat[h // 2 : h, 0 : w // 2]
            img_slide4 = img_mat[h // 2 : h, w // 2 : w]
            is_blur_1, score_1 = self.blur_infer(img_slide1)
            is_blur_2, score_2 = self.blur_infer(img_slide2)
            is_blur_3, score_3 = self.blur_infer(img_slide3)
            is_blur_4, score_4 = self.blur_infer(img_slide4)

            blur_state = False  # 模糊状态
            blur_score = 0.0  # 模糊分数
            # 切分后都为 True
            if is_blur_1 and is_blur_2 and is_blur_3 and is_blur_4:
                blur_state = True
                blur_score = base_score
                # return True, base_score
            else:  # 切分后存在 False
                fix_score = 0.25 * (score_1 + score_2 + score_3 + score_4)
                blur_state = False
                blur_score = fix_score
                # return False, fix_score

            # add thresh
            if blur_score >= 1 - self.blur_thresh:  # blur 是无模糊的分数，所以比较时阈值使用1-tresh
                blur_state = False
            else:
                blur_state = True
            return blur_state, blur_score
        else:  # False
            return False, base_score

    # 遮挡检测 处理
    def one_img_show(self, img):
        def checkSpace(img):
            def getpos_relyimgw(h, w):
                box_h = int((h - 160) / 2)
                if w < 2000:
                    box_w = int((w - 5) / 3)
                    for i in range(3):
                        posList.append(
                            ((5 + box_w * i, 100), (5 + box_w * (i + 1), 100 + box_h))
                        )
                    for i in range(3):
                        posList.append(
                            (
                                (5 + box_w * i, 100 + box_h + 30),
                                (5 + box_w * (i + 1), 100 + 2 * box_h + 30),
                            )
                        )
                if w > 2000:
                    box_w = int((w - 5) / 4)
                    for i in range(4):
                        posList.append(
                            ((5 + box_w * i, 100), (5 + box_w * (i + 1), 100 + box_h))
                        )
                    for i in range(4):
                        posList.append(
                            (
                                (5 + box_w * i, 100 + box_h + 30),
                                (5 + box_w * (i + 1), 100 + 2 * box_h + 30),
                            )
                        )

                return posList

            h, w, _ = img.shape
            posList = []
            posList = getpos_relyimgw(h, w)
            spaces = 0

            # 图像信息阈值参数
            val1 = 34
            val2 = 5
            val3 = 1100

            imgGray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            imgBlur = cv2.GaussianBlur(imgGray, (3, 3), 1)

            if val1 % 2 == 0:
                val1 += 1

            imgThres = cv2.adaptiveThreshold(
                imgBlur,
                255,
                cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                cv2.THRESH_BINARY_INV,
                val1,
                val2,
            )

            imgThres = cv2.medianBlur(imgThres, 5)
            kernel = np.ones((3, 3), np.uint8)

            imgThres = cv2.dilate(imgThres, kernel, iterations=1)
            cropList = []
            for pos in posList:
                (x1, y1), (x2, y2) = pos[0], pos[1]

                imgCrop = imgThres[y1:y2, x1:x2]
                count = cv2.countNonZero(imgCrop)

                if count < val3:
                    color = (0, 200, 0)
                    thics = 5
                    spaces += 1
                else:
                    color = (0, 0, 200)
                    thics = 2

            s_p = round(spaces / len(posList), 2)
            return img, spaces, s_p

        img, space, s_p = checkSpace(img)

        return space, s_p

    # 返回值封装（遮挡）
    def update_final_result_block(self, space, s_p):
        if space == 1:  # maybe
            detect_state = 0  # non-block
            detect_score = round(s_p, 4)  # 1 - score
        if space == 0:  # normal
            detect_state = 0  # non-block
            detect_score = round(s_p, 4)  # 1 - score
        # if space > 1 and space <= 6:  # shield, bug: 可能出现 space=8
        else:
            detect_state = 1  # block
            detect_score = round(s_p, 4)  # score
            if detect_score < self.area_thresh:
                detect_state = 0  # non-block
                detect_score = round(s_p, 4)  # 1 - score

        cls_map = process_configs["CLS_MAP"]
        detect_state = cls_map[int(detect_state)]

        final_result = {"DetectClass": detect_state, "DetectScore": detect_score}

        logger.info(
            f"Reqid: {self.reqid}, DetectClass: {detect_state}, SaveRule:{self.save_rule}"
        )

        return final_result, self.save_rule

    # 返回值封装（模糊、花屏）
    def update_final_result(self, is_state, score):
        # class True 1 / False 0
        cls_map = process_configs["CLS_MAP"]
        is_state = cls_map[int(is_state)]
        # score
        score = round(1 - float(score), 4)

        final_result = {"DetectClass": is_state, "DetectScore": score}

        logger.info(
            f"Reqid: {self.reqid}, DetectClass: {is_state}, SaveRule:{self.save_rule}"
        )

        return final_result, self.save_rule

    def run(
        self,
        img_arr,
        img_mat,
        area_thresh=CONFIG_AREA_THRESH,
        blur_thresh=CONFIG_BLUR_THRESH,
    ):
        # 参数设置
        self.area_thresh = area_thresh  # 面积（遮挡）阈值
        self.blur_thresh = blur_thresh  # 模糊阈值
        self.image_h, self.image_w = img_mat.shape[:2]  # 图片长宽，opencv
        logger.info(
            f"Reqid: {self.reqid}, image_h: {self.image_h}, image_w: {self.image_w}, area_thresh: {self.area_thresh}, blur_thresh: {self.blur_thresh}"
        )

        # 花屏检测
        mosaic_result, mosaic_score = self.mosaic_process(img_mat)
        logger.info(
            f"Reqid: {self.reqid}, mos: {mosaic_result}, score: {1 - mosaic_score}"
        )
        if mosaic_result is True:  # 出现花屏，直接返回，不进行模糊和遮挡判断
            logger.info(f"Reqid: {self.reqid}, is mosaic")
            return self.update_final_result(mosaic_result, mosaic_score)

        # 模糊检测
        blur_result, blur_score = self.blur_process(img_mat)
        logger.info(
            f"Reqid: {self.reqid}, blur: {blur_result}, score: {1 - blur_score}"
        )
        if blur_result is True:  # 出现模糊，直接返回，不进行遮挡判断
            logger.info(f"Reqid: {self.reqid}, is blur")
            return self.update_final_result(blur_result, blur_score)

        # 遮挡检测
        block_space, block_score = self.one_img_show(img_mat)
        logger.info(f"Reqid: {self.reqid}, space: {block_space}, score: {block_score}")

        # 遮挡检测
        return self.update_final_result_block(block_space, block_score)


class FileFormatValidation:
    @staticmethod
    def validate(file: bytes, mime_matches):
        if file is None or len(file) <= 128:
            return False

        info = fleep.get(file[:128])
        for mime in mime_matches:
            if info.mime_matches(mime):
                return True
        return False

    @staticmethod
    def convert_to_png(self):
        im = Image.open(BytesIO(self.file))
        byte_io = BytesIO()
        im.save(byte_io, "PNG")
        self.cleaned_image = byte_io.getvalue()


def check_params(reqid, params):
    # 请求体为字典
    if not isinstance(params, dict):
        logger.info(f"Reqid: {reqid}, body type err, return code: 400005")
        raise StatusException(HTTPStatus.BODY_TYPE_ERR)

    # Action and ImageData
    Action = params.get("Action", None)
    ImageData = params.get("ImageData", None)

    if Action is None or ImageData is None:
        logger.info(f"Reqid: {reqid}, Action or ImageData missing, return code: 400006")
        raise StatusException(HTTPStatus.MUST_PRAM_ERR)
    if not isinstance(Action, str) or not isinstance(ImageData, str):
        logger.info(
            f"Reqid: {reqid}, Action or ImageData type err, return code: 400008"
        )
        raise StatusException(HTTPStatus.PRAM_TYPE_ERR)
    if not Action or not ImageData:
        logger.info(f"Reqid: {reqid}, Action or ImageData empty, return code: 400009")
        raise StatusException(HTTPStatus.IMAGE_DATA_AND_ACTION_EMPTY_ERR)
    if Action != action_configs:  # ACTION
        logger.info(f"Reqid: {reqid}, Action value err, return code: 400010")
        raise StatusException(HTTPStatus.ACTION_VALUE_ERR)

    # base64 解码
    try:
        img_byte = base64.urlsafe_b64decode(ImageData)
    except Exception as e:
        logger.info(
            f"Reqid: {reqid}, decode image base64 err: {e}, return code: 400011"
        )
        raise StatusException(HTTPStatus.IMAGE_DATA_BASE64_ERR)

    # 图片格式
    if not FileFormatValidation.validate(img_byte, params_configs["FILE_FORMAT"]):
        logger.info(f"Reqid: {reqid}, image format err, return code: 400012")
        raise StatusException(HTTPStatus.IMAGE_TYPE_ERR)

    # 图片大小
    if len(img_byte) > params_configs["IMAGE_SIZE"]:
        logger.info(f"Reqid: {reqid}, image size err, return code: 400013")
        raise StatusException(HTTPStatus.IMAGE_SIZE_ERR)

    # 图片解码
    try:
        img_arr = np.frombuffer(img_byte, np.uint8)
        img_mat = cv2.imdecode(img_arr, cv2.IMREAD_COLOR)  # BGR
        height, width = img_mat.shape[:2]
    except Exception as e:
        logger.info(f"Reqid: {reqid}, decode image err: {e}, return code: 410001")
        raise StatusException(HTTPStatus.IMAGE_DECODE_ERR)

    # 图片长宽
    if not (
        params_configs["MIN_LEN"] <= height <= params_configs["MAX_LEN"]
        and params_configs["MIN_LEN"] <= width <= params_configs["MAX_LEN"]
    ):
        logger.info(f"Reqid: {reqid}, image shape err, return code: 410002")
        raise StatusException(HTTPStatus.IMAGE_SHAPE_ERR)

    # 业务字段 面积（遮挡）阈值
    area_thresh = params.get("AreaThresh", CONFIG_AREA_THRESH)
    if not (isinstance(area_thresh, int) or isinstance(area_thresh, float)):
        logger.info(
            f"Reqid: {reqid}, AreaThresh data type is illegal, return code: 450035"
        )
        raise StatusException(HTTPStatus.AREA_THRESH_FLOAT_TYPE_ERR)
    if not 0 <= area_thresh <= 1:
        logger.info(f"Reqid: {reqid}, AreaThresh data is illegal, return code: 450036")
        raise StatusException(HTTPStatus.AREA_THRESH_VALUE_ERR)

    # 业务字段 模糊阈值
    # TODO 错误码未给出
    blur_thresh = params.get("BlurThresh", CONFIG_AREA_THRESH)
    if not (isinstance(blur_thresh, int) or isinstance(blur_thresh, float)):
        logger.info(
            f"Reqid: {reqid}, BlurThresh data type is illegal, return code: 450035"
        )
        raise StatusException(HTTPStatus.AREA_THRESH_FLOAT_TYPE_ERR)
    if not 0 <= blur_thresh <= 1:
        logger.info(f"Reqid: {reqid}, BlurThresh data is illegal, return code: 450036")
        raise StatusException(HTTPStatus.AREA_THRESH_VALUE_ERR)
    return img_arr, img_mat, area_thresh, blur_thresh


def run(reqid, params):
    # 参数校验及初始化
    time_0 = time.time()
    img_arr, img_mat, area_thresh, blur_thresh = check_params(reqid, params)
    client = BlockDetect(process_configs, reqid)

    # 推理执行
    time_1 = time.time()
    results, save_rule = client.run(img_arr, img_mat, area_thresh, blur_thresh)
    time_2 = time.time()

    logger.info(
        f"Reqid: {reqid}, check params: {(time_1 - time_0) * 1000} ms, infer time: {(time_2 - time_1) * 1000} ms"
    )

    return results, save_rule


def encode_base64_string(file_path):
    with open(file_path, "rb") as f:
        encoded_string = str(base64.urlsafe_b64encode(f.read()), "utf-8")
    return encoded_string


if __name__ == "__main__":
    log.basicConfig(
        level=log.DEBUG,
        format="%(asctime)s %(levelname)s %(filename)s:%(lineno)d - %(message)s",
    )
    client = BlockDetect(process_configs)

    image_paths = glob.glob("../../test/mosaic*")
    image_paths = glob.glob("../../test/blur*")
    image_paths = glob.glob("../../test/block*")
    image_paths = glob.glob("../../test/normal*")
    image_paths = sorted(image_paths)

    for idx, image_path in enumerate(image_paths):
        ic(idx, image_path)
        test = cv2.imread(image_path)

        with open(image_path, "rb") as file:
            img_arr = np.frombuffer(file.read(), np.uint8)
        img_mat = cv2.imdecode(img_arr, cv2.IMREAD_COLOR)  # BGR

        final_result, _ = client.run(img_arr, img_mat)
        ic(final_result)
