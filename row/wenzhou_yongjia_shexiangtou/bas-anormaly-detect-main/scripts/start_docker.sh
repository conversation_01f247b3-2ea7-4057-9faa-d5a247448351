NAME=$(cat ../__NAME)
IMAGE="harbor.ctyuncdn.cn/ai-service/algorithm/${NAME}"
VERSION=$(cat ../__VERSION)

# 新版接口规范
sudo docker run -it --rm \
  --net=host \
  --name=${NAME}-${VERSION} \
  -e SERVICE_NAME=${NAME} \
  -e SERVICE_VERSION=${VERSION} \
  -e HTTP_PORT=8059 \
  -e PROCESS_NUM=20 \
  -e HANDLER_PATH=/app/client \
  -e HANDLER_NAME=anormaly_detect \
  -e OMP_NUM_THREADS=5 \
  -e OPENBLAS_NUM_THREADS=5 \
  -e MKL_NUM_THREADS=5 \
  -e VECLIB_MAXIMUM_THREADS=5 \
  -e NUMEXPR_NUM_THREADS=5 \
  -e TRITON_URL='localhost:7610' \
  --cpus=16 \
  -m 4096m \
  ${IMAGE}:${VERSION}

# -e BACKFLOW=True \
# -e STORAGE=oss \
# -e COUNTER=True \
# -e COUNTER_URL=http://**********:9211/storage \
# -e APOLLO_ENABLE=True \

# ********** / ************* 流量统计地址
