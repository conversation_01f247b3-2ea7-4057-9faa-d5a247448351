# -*- coding:utf-8 -*-
import os
import sys
import requests
import base64
import json
from multiprocessing import Process
from multiprocessing import Queue
from multiprocessing import Value
import time

# fastapi 服务并发测试工具


import logging as log

log.basicConfig(
    level=log.INFO,
    format="%(asctime)s %(levelname)s %(filename)s:%(lineno)d - %(message)s",
)

is_exit = Value("b", False)


def timing(minutes):
    start = time.time()
    while True:
        if start + minutes * 60 < time.time():
            # 超过指定时间
            is_exit.value = True
            print("process exit")
            break
        else:
            time.sleep(0.01)


def gen_encoded_string(img_path):
    with open(img_path, "rb") as f:
        encoded_string = str(base64.urlsafe_b64encode(f.read()), "utf-8")
    return encoded_string


def generate_json_request(config):
    task_name = config.get("Action")
    file_path = config.get("file_path")

    json_request = {"Action": task_name, "ImageData": gen_encoded_string(file_path)}
    return json_request


def process_request(target_url, json_request, q):
    headers = {"content-type": "application/json", "Connection": "close"}
    start = time.time()
    total_num = 0
    timeout = 300

    sess = requests.Session()
    sess.mount("http://", requests.adapters.HTTPAdapter(max_retries=1))

    while not is_exit.value:
        try:
            resp_data = json.dumps(json_request)
            resp = sess.post(
                url=target_url, data=resp_data, headers=headers, timeout=timeout
            )
            if resp.status_code != 200:
                print("Send fail.")
            else:
                json_data = json.loads(resp.text)
                if json_data.get("code") == 0:
                    # print(json_data)
                    pass
                else:
                    print("[ERROR]")
                    print(json_data)
            total_num += 1
        except Exception as e:
            log.warning(
                "post timeout.{}. {}".format(
                    time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()), str(e)
                )
            )
            continue
    end = time.time()
    q.put([int(end - start) * 1000, total_num])


def run_multi_task(config):
    print(config)
    target_url = config.get("target_url")
    task_name = config.get("Action")
    batch_sizes = config.get("batch_sizes")
    minutes = config.get("minutes")
    nums = config.get("nums")
    file_path = config.get("file_path")

    save_dir_name = "minitue_" + task_name + "_" + os.path.basename(file_path)
    os.makedirs(save_dir_name, mode=0o777, exist_ok=True)

    # warm up
    if len(sys.argv) > 1:
        num = 64
        warm_up_minutes = 1  # min
        print("warm up start, process num :", num)
        processes = []
        q = Queue()
        json_request = generate_json_request(config)
        for i in range(num):
            processes.append(
                Process(target=process_request, args=(target_url, json_request, q))
            )
        processes.append(Process(target=timing, args=(warm_up_minutes,)))
        for process in processes:
            process.start()
        for process in processes:
            process.join()
        is_exit.value = False
        print("warm up finish")
        sys.exit()

    result_txts = []
    for batch_size in batch_sizes:
        for num in nums:
            processes = []
            total_count = 0
            total_time = 0
            q = Queue()

            json_request = generate_json_request(config)

            for i in range(num):
                processes.append(
                    Process(target=process_request, args=(target_url, json_request, q))
                )

            processes.append(Process(target=timing, args=(minutes,)))
            print(
                "minutes %d, batch_size %d, 并发 num %d, processes start"
                % (minutes, batch_size, num)
            )
            for process in processes:
                process.start()
            for process in processes:
                process.join()
            save_file = (
                "minutes_"
                + str(minutes)
                + "_batch_size_"
                + str(batch_size)
                + "_num_"
                + str(num)
                + ".txt"
            )
            save_path = os.path.join(save_dir_name, save_file)
            result_txts.append(save_path)
            with open(save_path, "w", encoding="utf-8") as f:
                f.write("batch_size\tnum\tcost\tbatchs\n")
                while not q.empty():
                    data = q.get()  # cost_time, run_batch_num
                    total_count += data[1]
                    total_time += data[0]
                    f.write(
                        str(batch_size)
                        + "\t"
                        + str(num)
                        + "\t"
                        + str(data[0])
                        + "\t"
                        + str(data[1])
                        + "\n"
                    )
                    # , ', 延时:', data[0]/(batch_size*num*data[1]), 'ms\n')
                    print("吞吐量:", 1000 * batch_size * num * data[1] / data[0])
                print("总吞吐量:", 1000 * num * total_count / total_time)
            is_exit.value = False
            for process in processes:
                if process.is_alive:
                    process.terminate()
                    # process.join()

            print("time sleep 10s")
            time.sleep(10)


if __name__ == "__main__":
    config = {
        "target_url": "http://localhost:8059/predict",
        "Action": "AnormalyDetect",
        "batch_sizes": [1],
        "minutes": 1,
        "nums": [2, 4, 8, 16, 32, 64],  # 并发数
        "nums": [16, 32, 64],  # 并发数
        "file_path": "../test/mosaic-3.jpg",
        "file_path": "../test/mosaic-4.jpg",
        "file_path": "../test/blur-1.jpg",
        "file_path": "../test/blur-5.jpg",
        "file_path": "../test/block-1.png",
        "file_path": "../test/block-6.jpg",
    }
    run_multi_task(config)
