# -*- coding:utf-8 -*-
import json
import requests
import base64
import glob
from icecream import ic

# 服务调用脚本


def encode_base64_string(file_path):
    with open(file_path, "rb") as f:
        encoded_string = str(base64.urlsafe_b64encode(f.read()), "utf-8")
    return encoded_string


if __name__ == "__main__":
    app_key = "<EMAIL>"
    token = "MzIxNmFmOWU3MTViOTI4YzcyODVmMmYxYmE0ZjhkYjE6MTY2MTQ5NTAxOS42OTk2NjU1"

    # target_url = 'http://*************:8502/ai'
    target_url = "http://localhost:8059/predict"

    file_paths = glob.glob("../test/mosaic*")
    # file_paths = glob.glob("../test/blur*")
    # file_paths = glob.glob("../test/block*")
    # file_paths = glob.glob("../test/normal*")
    file_paths.sort()

    for idx, file_path in enumerate(file_paths):
        ic(idx, file_path)

        base64_string = encode_base64_string(file_path)
        headers = {"content-type": "application/json"}

        json_data = {
            "Action": "AnormalyDetect",
            "ImageData": base64_string,
            "AppKey": app_key,
            "Token": token,
        }

        resp_data = json.dumps(json_data)
        resp = requests.post(target_url, data=resp_data, headers=headers, timeout=300)
        if resp.status_code != 200:
            print("Send fail.")
        else:
            json_data = json.loads(resp.text)
            if json_data.get("code") == 0:
                print("[SUCESS]")
                ic(json_data)
            else:
                print("[ERROR]")
                ic(json_data)
