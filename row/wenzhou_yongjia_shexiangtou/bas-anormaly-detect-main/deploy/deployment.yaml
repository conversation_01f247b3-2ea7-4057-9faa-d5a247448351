---
## service
kind: Service
apiVersion: v1
metadata:
  name: {{DEPLOYMENT_NAME}}
  namespace: {{NAMESPACE}}
spec:
  selector:
    app: {{DEPLOYMENT_NAME}}
  ports:
    - protocol: TCP
      port: 80
      targetPort: {{SERVICE_TARGET_PORT}}

---
## ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{DEPLOYMENT_NAME}}
  namespace: {{NAMESPACE}}
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "1000m"
spec:
  ingressClassName: nginx
  rules:
  - host: {{DEPLOYMENT_NAME}}-{{NAMESPACE}}.ai.test.ctcdn.cn
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: {{DEPLOYMENT_NAME}}
            port:
              number: 80
              
---
## deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: {{DEPLOYMENT_NAME}}
  name: {{DEPLOYMENT_NAME}}
  namespace: {{NAMESPACE}}
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: {{DEPLOYMENT_NAME}}
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: {{DEPLOYMENT_NAME}}
    spec:
      dnsConfig:
        options:
          - name: single-request-reopen
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: gpu
                    operator: In
                    values:
                      - disable
      containers:
        - image: {{imageUrl}}
          imagePullPolicy: IfNotPresent
          name: {{DEPLOYMENT_NAME}}
          env:
          - name: SERVICE_NAME
            value: {{DEPLOYMENT_NAME}}
          - name: HTTP_PORT
            value: "{{SERVICE_TARGET_PORT}}"
          - name: PROCESS_NUM
            value: "20"
          - name: HANDLER_PATH
            value: /app/client
          - name: HANDLER_NAME
            value: anormaly_detect
          - name: OMP_NUM_THREADS
            value: "5"
          - name: OPENBLAS_NUM_THREADS
            value: "5"
          - name: MKL_NUM_THREADS
            value: "5"
          - name: VECLIB_MAXIMUM_THREADS
            value: "5"
          - name: NUMEXPR_NUM_THREADS
            value: "5"
          - name: TRITON_URL
            value: triton-anormaly-detect:80
          - name: BACKFLOW
            value: "True" # 数据回流
          - name: STORAGE
            value: oss
          - name: COUNTER
            value: "True" # 流量统计
          - name: COUNTER_URL
            value: http://124.72.136.48:9211/storage # 流量统计地址
          - name: APOLLO_ENABLE
            value: "True" # 配置中心
          resources:
            requests:
              cpu: "1"
              memory: 1Gi
            limits:
              cpu: "16"
              memory: 4Gi
          livenessProbe:
            tcpSocket:
              port: {{SERVICE_TARGET_PORT}}
            initialDelaySeconds: 300
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: {{SERVICE_TARGET_PORT}}
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          ports:
          - containerPort: {{SERVICE_TARGET_PORT}}
      restartPolicy: Always

