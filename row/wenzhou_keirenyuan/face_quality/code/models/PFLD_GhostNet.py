#!/usr/bin/env python3
# -*- coding:utf-8 -*-


import torch
from torch.nn import <PERSON><PERSON><PERSON>, AvgPool2d, Linear
from models.base_module import Conv_Block, GhostBottleneck
import torch.nn.functional as F
from fightingcv_attention.attention.ECAAttention import ECAAttention


class PFLD_GhostNet_Backbone(Module):
    def __init__(self, width_factor=1, input_size=112, landmark_number=98):
        super(PFLD_GhostNet_Backbone, self).__init__()

        self.conv1 = Conv_Block(3, int(64 * width_factor), 3, 2, 1)
        self.conv2 = Conv_Block(int(64 * width_factor), int(64 * width_factor), 3, 1, 1, group=int(64 * width_factor))

        self.conv3_1 = GhostBottleneck(int(64 * width_factor), int(128 * width_factor), int(80 * width_factor), stride=2)
        self.conv3_2 = GhostBottleneck(int(80 * width_factor), int(160 * width_factor), int(80 * width_factor), stride=1)
        self.conv3_3 = GhostB<PERSON>lene<PERSON>(int(80 * width_factor), int(160 * width_factor), int(80 * width_factor), stride=1)

        self.conv4_1 = GhostBottleneck(int(80 * width_factor), int(240 * width_factor), int(96 * width_factor), stride=2)
        self.conv4_2 = GhostBottleneck(int(96 * width_factor), int(288 * width_factor), int(96 * width_factor), stride=1)
        self.conv4_3 = GhostBottleneck(int(96 * width_factor), int(288 * width_factor), int(96 * width_factor), stride=1)

        self.conv5_1 = GhostBottleneck(int(96 * width_factor), int(384 * width_factor), int(144 * width_factor), stride=2)
        self.conv5_2 = GhostBottleneck(int(144 * width_factor), int(576 * width_factor), int(144 * width_factor), stride=1)
        self.conv5_3 = GhostBottleneck(int(144 * width_factor), int(576 * width_factor), int(144 * width_factor), stride=1)
        self.conv5_4 = GhostBottleneck(int(144 * width_factor), int(576 * width_factor), int(144 * width_factor), stride=1)

        self.conv6 = GhostBottleneck(int(144 * width_factor), int(288 * width_factor), int(16 * width_factor), stride=1)
        self.conv7 = Conv_Block(int(16 * width_factor), int(32 * width_factor), 3, 1, 1)
        self.conv8 = Conv_Block(int(32 * width_factor), int(128 * width_factor), input_size // 16, 1, 0, has_bn=False)


    def forward(self, x):
        x = self.conv1(x)
        x1 = self.conv2(x)

        x = self.conv3_1(x1)
        x = self.conv3_2(x)
        x2 = self.conv3_3(x)

        x = self.conv4_1(x2)
        x = self.conv4_2(x)
        x3 = self.conv4_3(x)

        x = self.conv5_1(x3)
        x = self.conv5_2(x)
        x = self.conv5_3(x)
        x4 = self.conv5_4(x)

        x = self.conv6(x4)
        x = self.conv7(x)
        x5 = self.conv8(x)

        return x1, x2, x3, x4, x5


class PFLD_GhostNet(Module):
    def __init__(self, backbone, width_factor=1, input_size=112, landmark_number=150, export=False):
        super(PFLD_GhostNet, self).__init__()
        self.export = export
        self.backbone = backbone
        self.avg_pool1 = AvgPool2d(input_size // 2)
        self.avg_pool2 = AvgPool2d(input_size // 4)
        self.avg_pool3 = AvgPool2d(input_size // 8)
        self.avg_pool4 = AvgPool2d(input_size // 16)

        self.landmark_fc = Linear(int(512 * width_factor), landmark_number * 2)

        self.attention_cls = ECAAttention()

        self.attention_blur = ECAAttention()

        self.attention_illu = ECAAttention()

        self.cls_fc = Linear(128, 4)  # 人脸分类，0人脸，1口罩，2非人脸，3侧脸

        self.blur_fc = Linear(128, 1)  # 人脸模糊

        self.illu_fc = Linear(128, 1)  # 人脸亮度

    def forward(self, x):
        x1, x2, x3, x4, x5 = self.backbone(x)

        lmk_x1 = self.avg_pool1(x1)
        lmk_x1 = lmk_x1.view(lmk_x1.size(0), -1)
        lmk_x2 = self.avg_pool2(x2)
        lmk_x2 = lmk_x2.view(lmk_x2.size(0), -1)
        lmk_x3 = self.avg_pool3(x3)
        lmk_x3 = lmk_x3.view(lmk_x3.size(0), -1)
        lmk_x4 = self.avg_pool4(x4)
        lmk_x4 = lmk_x4.view(lmk_x4.size(0), -1)
        lmk_x5 = x5.view(x5.size(0), -1)
        lmk_multi_scale = torch.cat(
            [lmk_x1, lmk_x2, lmk_x3, lmk_x4, lmk_x5], 1)

        lmk = self.landmark_fc(lmk_multi_scale)

        cls_x = self.attention_cls(x5)
        cls_x = cls_x.view(cls_x.size(0), -1)
        cls = self.cls_fc(cls_x)

        blur_x = self.attention_blur(x5)
        blur_x = blur_x.view(blur_x.size(0), -1)
        blur = self.blur_fc(blur_x)

        illu_x = self.attention_illu(x5)
        illu_x = illu_x.view(illu_x.size(0), -1)
        illu = self.illu_fc(illu_x)

        if self.export:
            return torch.cat((lmk, cls, blur, illu), dim=1)

        return lmk, cls, blur, illu


# class PFLD_Ultralight_AuxiliaryNet(Module):
#     def __init__(self, width_factor=1):
#         super(PFLD_Ghost_AuxiliaryNet, self).__init__()
#         self.conv1 = Conv_Block(int(64 * width_factor), int(64 * width_factor), 1, 1, 0)
#         self.conv2 = Conv_Block(int(80 * width_factor), int(64 * width_factor), 1, 1, 0)
#         self.conv3 = Conv_Block(int(96 * width_factor), int(64 * width_factor), 1, 1, 0)
#         self.conv4 = Conv_Block(int(144 * width_factor), int(64 * width_factor), 1, 1, 0)

#         self.merge1 = Conv_Block(int(64 * width_factor), int(64 * width_factor), 3, 1, 1)
#         self.merge2 = Conv_Block(int(64 * width_factor), int(64 * width_factor), 3, 1, 1)
#         self.merge3 = Conv_Block(int(64 * width_factor), int(64 * width_factor), 3, 1, 1)

#         self.conv_out = Conv_Block(int(64 * width_factor), 1, 1, 1, 0)

#     def forward(self, out1, out2, out3, out4):
#         output1 = self.conv1(out1)
#         output2 = self.conv2(out2)
#         output3 = self.conv3(out3)
#         output4 = self.conv4(out4)

#         up4 = F.interpolate(output4, size=[output3.size(2), output3.size(3)], mode="nearest")
#         output3 = output3 + up4
#         output3 = self.merge3(output3)

#         up3 = F.interpolate(output3, size=[output2.size(2), output2.size(3)], mode="nearest")
#         output2 = output2 + up3
#         output2 = self.merge2(output2)

#         up2 = F.interpolate(output2, size=[output1.size(2), output1.size(3)], mode="nearest")
#         output1 = output1 + up2
#         output1 = self.merge1(output1)

#         output1 = self.conv_out(output1)

#         return output1

# if __name__ == "__main__":
#     model = PFLD_GhostNet(
#         PFLD_GhostNet_Backbone(1, 112, 150), 1, 112, 150)
#     print(model.state_dict().keys())
#     input_data = torch.randn((4, 3, 112, 112))
#     lmk, cls, blur, illu = model(input_data)

#     print(lmk.shape, cls.shape, blur.shape, illu.shape)
