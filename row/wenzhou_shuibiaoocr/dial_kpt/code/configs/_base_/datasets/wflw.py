dataset_info = dict(
    dataset_name='wflw',
    paper_info=dict(
        author='<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, '
        '<PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>',
        title='Look at boundary: A boundary-aware face alignment algorithm',
        container='Proceedings of the IEEE conference on computer '
        'vision and pattern recognition',
        year='2018',
        homepage='https://wywu.github.io/projects/LAB/WFLW.html',
    ),
    keypoint_info={
        0:
        dict(
            name='kpt-0', id=0, color=[255, 255, 255], type='', swap='kpt-32'),
        1:
        dict(
            name='kpt-1', id=1, color=[255, 255, 255], type='', swap='kpt-31'),
        2:
        dict(
            name='kpt-2', id=2, color=[255, 255, 255], type='', swap='kpt-30'),
        3:
        dict(
            name='kpt-3', id=3, color=[255, 255, 255], type='', swap='kpt-29'),
        4:
        dict(
            name='kpt-4', id=4, color=[255, 255, 255], type='', swap='kpt-28'),
        5:
        dict(
            name='kpt-5', id=5, color=[255, 255, 255], type='', swap='kpt-27'),
        6:
        dict(
            name='kpt-6', id=6, color=[255, 255, 255], type='', swap='kpt-26'),
        7:
        dict(
            name='kpt-7', id=7, color=[255, 255, 255], type='', swap='kpt-25'),
        8:
        dict(
            name='kpt-8', id=8, color=[255, 255, 255], type='', swap='kpt-24'),
        9:
        dict(
            name='kpt-9', id=9, color=[255, 255, 255], type='', swap='kpt-23'),
        10:
        dict(
            name='kpt-10',
            id=10,
            color=[255, 255, 255],
            type='',
            swap='kpt-22'),
        11:
        dict(
            name='kpt-11',
            id=11,
            color=[255, 255, 255],
            type='',
            swap='kpt-21'),
        12:
        dict(
            name='kpt-12',
            id=12,
            color=[255, 255, 255],
            type='',
            swap='kpt-20'),
        13:
        dict(
            name='kpt-13',
            id=13,
            color=[255, 255, 255],
            type='',
            swap='kpt-19'),
        14:
        dict(
            name='kpt-14',
            id=14,
            color=[255, 255, 255],
            type='',
            swap='kpt-18'),
        15:
        dict(
            name='kpt-15',
            id=15,
            color=[255, 255, 255],
            type='',
            swap='kpt-17'),
        16:
        dict(name='kpt-16', id=16, color=[255, 255, 255], type='', swap=''),
        17:
        dict(
            name='kpt-17',
            id=17,
            color=[255, 255, 255],
            type='',
            swap='kpt-15'),
        18:
        dict(
            name='kpt-18',
            id=18,
            color=[255, 255, 255],
            type='',
            swap='kpt-14'),
        19:
        dict(
            name='kpt-19',
            id=19,
            color=[255, 255, 255],
            type='',
            swap='kpt-13'),
        20:
        dict(
            name='kpt-20',
            id=20,
            color=[255, 255, 255],
            type='',
            swap='kpt-12'),
        21:
        dict(
            name='kpt-21',
            id=21,
            color=[255, 255, 255],
            type='',
            swap='kpt-11'),
        22:
        dict(
            name='kpt-22',
            id=22,
            color=[255, 255, 255],
            type='',
            swap='kpt-10'),
        23:
        dict(
            name='kpt-23', id=23, color=[255, 255, 255], type='',
            swap='kpt-9'),
        24:
        dict(
            name='kpt-24', id=24, color=[255, 255, 255], type='',
            swap='kpt-8'),
        25:
        dict(
            name='kpt-25', id=25, color=[255, 255, 255], type='',
            swap='kpt-7'),
        26:
        dict(
            name='kpt-26', id=26, color=[255, 255, 255], type='',
            swap='kpt-6'),
        27:
        dict(
            name='kpt-27', id=27, color=[255, 255, 255], type='',
            swap='kpt-5'),
        28:
        dict(
            name='kpt-28', id=28, color=[255, 255, 255], type='',
            swap='kpt-4'),
        29:
        dict(
            name='kpt-29', id=29, color=[255, 255, 255], type='',
            swap='kpt-3'),
        30:
        dict(
            name='kpt-30', id=30, color=[255, 255, 255], type='',
            swap='kpt-2'),
        31:
        dict(
            name='kpt-31', id=31, color=[255, 255, 255], type='',
            swap='kpt-1'),
        32:
        dict(
            name='kpt-32', id=32, color=[255, 255, 255], type='',
            swap='kpt-0'),
        33:
        dict(
            name='kpt-33',
            id=33,
            color=[255, 255, 255],
            type='',
            swap='kpt-46'),
        34:
        dict(
            name='kpt-34',
            id=34,
            color=[255, 255, 255],
            type='',
            swap='kpt-45'),
        35:
        dict(
            name='kpt-35',
            id=35,
            color=[255, 255, 255],
            type='',
            swap='kpt-44'),
        36:
        dict(
            name='kpt-36',
            id=36,
            color=[255, 255, 255],
            type='',
            swap='kpt-43'),
        37:
        dict(
            name='kpt-37',
            id=37,
            color=[255, 255, 255],
            type='',
            swap='kpt-42'),
        38:
        dict(
            name='kpt-38',
            id=38,
            color=[255, 255, 255],
            type='',
            swap='kpt-50'),
        39:
        dict(
            name='kpt-39',
            id=39,
            color=[255, 255, 255],
            type='',
            swap='kpt-49'),
        40:
        dict(
            name='kpt-40',
            id=40,
            color=[255, 255, 255],
            type='',
            swap='kpt-48'),
        41:
        dict(
            name='kpt-41',
            id=41,
            color=[255, 255, 255],
            type='',
            swap='kpt-47'),
        42:
        dict(
            name='kpt-42',
            id=42,
            color=[255, 255, 255],
            type='',
            swap='kpt-37'),
        43:
        dict(
            name='kpt-43',
            id=43,
            color=[255, 255, 255],
            type='',
            swap='kpt-36'),
        44:
        dict(
            name='kpt-44',
            id=44,
            color=[255, 255, 255],
            type='',
            swap='kpt-35'),
        45:
        dict(
            name='kpt-45',
            id=45,
            color=[255, 255, 255],
            type='',
            swap='kpt-34'),
        46:
        dict(
            name='kpt-46',
            id=46,
            color=[255, 255, 255],
            type='',
            swap='kpt-33'),
        47:
        dict(
            name='kpt-47',
            id=47,
            color=[255, 255, 255],
            type='',
            swap='kpt-41'),
        48:
        dict(
            name='kpt-48',
            id=48,
            color=[255, 255, 255],
            type='',
            swap='kpt-40'),
        49:
        dict(
            name='kpt-49',
            id=49,
            color=[255, 255, 255],
            type='',
            swap='kpt-39'),
        50:
        dict(
            name='kpt-50',
            id=50,
            color=[255, 255, 255],
            type='',
            swap='kpt-38'),
        51:
        dict(name='kpt-51', id=51, color=[255, 255, 255], type='', swap=''),
        52:
        dict(name='kpt-52', id=52, color=[255, 255, 255], type='', swap=''),
        53:
        dict(name='kpt-53', id=53, color=[255, 255, 255], type='', swap=''),
        54:
        dict(name='kpt-54', id=54, color=[255, 255, 255], type='', swap=''),
        55:
        dict(
            name='kpt-55',
            id=55,
            color=[255, 255, 255],
            type='',
            swap='kpt-59'),
        56:
        dict(
            name='kpt-56',
            id=56,
            color=[255, 255, 255],
            type='',
            swap='kpt-58'),
        57:
        dict(name='kpt-57', id=57, color=[255, 255, 255], type='', swap=''),
        58:
        dict(
            name='kpt-58',
            id=58,
            color=[255, 255, 255],
            type='',
            swap='kpt-56'),
        59:
        dict(
            name='kpt-59',
            id=59,
            color=[255, 255, 255],
            type='',
            swap='kpt-55'),
        60:
        dict(
            name='kpt-60',
            id=60,
            color=[255, 255, 255],
            type='',
            swap='kpt-72'),
        61:
        dict(
            name='kpt-61',
            id=61,
            color=[255, 255, 255],
            type='',
            swap='kpt-71'),
        62:
        dict(
            name='kpt-62',
            id=62,
            color=[255, 255, 255],
            type='',
            swap='kpt-70'),
        63:
        dict(
            name='kpt-63',
            id=63,
            color=[255, 255, 255],
            type='',
            swap='kpt-69'),
        64:
        dict(
            name='kpt-64',
            id=64,
            color=[255, 255, 255],
            type='',
            swap='kpt-68'),
        65:
        dict(
            name='kpt-65',
            id=65,
            color=[255, 255, 255],
            type='',
            swap='kpt-75'),
        66:
        dict(
            name='kpt-66',
            id=66,
            color=[255, 255, 255],
            type='',
            swap='kpt-74'),
        67:
        dict(
            name='kpt-67',
            id=67,
            color=[255, 255, 255],
            type='',
            swap='kpt-73'),
        68:
        dict(
            name='kpt-68',
            id=68,
            color=[255, 255, 255],
            type='',
            swap='kpt-64'),
        69:
        dict(
            name='kpt-69',
            id=69,
            color=[255, 255, 255],
            type='',
            swap='kpt-63'),
        70:
        dict(
            name='kpt-70',
            id=70,
            color=[255, 255, 255],
            type='',
            swap='kpt-62'),
        71:
        dict(
            name='kpt-71',
            id=71,
            color=[255, 255, 255],
            type='',
            swap='kpt-61'),
        72:
        dict(
            name='kpt-72',
            id=72,
            color=[255, 255, 255],
            type='',
            swap='kpt-60'),
        73:
        dict(
            name='kpt-73',
            id=73,
            color=[255, 255, 255],
            type='',
            swap='kpt-67'),
        74:
        dict(
            name='kpt-74',
            id=74,
            color=[255, 255, 255],
            type='',
            swap='kpt-66'),
        75:
        dict(
            name='kpt-75',
            id=75,
            color=[255, 255, 255],
            type='',
            swap='kpt-65'),
        76:
        dict(
            name='kpt-76',
            id=76,
            color=[255, 255, 255],
            type='',
            swap='kpt-82'),
        77:
        dict(
            name='kpt-77',
            id=77,
            color=[255, 255, 255],
            type='',
            swap='kpt-81'),
        78:
        dict(
            name='kpt-78',
            id=78,
            color=[255, 255, 255],
            type='',
            swap='kpt-80'),
        79:
        dict(name='kpt-79', id=79, color=[255, 255, 255], type='', swap=''),
        80:
        dict(
            name='kpt-80',
            id=80,
            color=[255, 255, 255],
            type='',
            swap='kpt-78'),
        81:
        dict(
            name='kpt-81',
            id=81,
            color=[255, 255, 255],
            type='',
            swap='kpt-77'),
        82:
        dict(
            name='kpt-82',
            id=82,
            color=[255, 255, 255],
            type='',
            swap='kpt-76'),
        83:
        dict(
            name='kpt-83',
            id=83,
            color=[255, 255, 255],
            type='',
            swap='kpt-87'),
        84:
        dict(
            name='kpt-84',
            id=84,
            color=[255, 255, 255],
            type='',
            swap='kpt-86'),
        85:
        dict(name='kpt-85', id=85, color=[255, 255, 255], type='', swap=''),
        86:
        dict(
            name='kpt-86',
            id=86,
            color=[255, 255, 255],
            type='',
            swap='kpt-84'),
        87:
        dict(
            name='kpt-87',
            id=87,
            color=[255, 255, 255],
            type='',
            swap='kpt-83'),
        88:
        dict(
            name='kpt-88',
            id=88,
            color=[255, 255, 255],
            type='',
            swap='kpt-92'),
        89:
        dict(
            name='kpt-89',
            id=89,
            color=[255, 255, 255],
            type='',
            swap='kpt-91'),
        90:
        dict(name='kpt-90', id=90, color=[255, 255, 255], type='', swap=''),
        91:
        dict(
            name='kpt-91',
            id=91,
            color=[255, 255, 255],
            type='',
            swap='kpt-89'),
        92:
        dict(
            name='kpt-92',
            id=92,
            color=[255, 255, 255],
            type='',
            swap='kpt-88'),
        93:
        dict(
            name='kpt-93',
            id=93,
            color=[255, 255, 255],
            type='',
            swap='kpt-95'),
        94:
        dict(name='kpt-94', id=94, color=[255, 255, 255], type='', swap=''),
        95:
        dict(
            name='kpt-95',
            id=95,
            color=[255, 255, 255],
            type='',
            swap='kpt-93'),
        96:
        dict(
            name='kpt-96',
            id=96,
            color=[255, 255, 255],
            type='',
            swap='kpt-97'),
        97:
        dict(
            name='kpt-97',
            id=97,
            color=[255, 255, 255],
            type='',
            swap='kpt-96')
    },
    skeleton_info={},
    joint_weights=[1.] * 98,
    sigmas=[])
