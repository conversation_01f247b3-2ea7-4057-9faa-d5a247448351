# bas-mask-detect 口罩检测

### 0. 文档说明

- **版本信息**：

  - **当前版本**：0.3.3
  - **端口号**：7090（triton）、8012（backend）
  - **镜像版本**：[harbor.ctyuncdn.cn/ai-service/algorithm/bas-mask-detect:0.3.3]()
  - **fastapi 框架版本**：[harbor.ctyuncdn.cn/ai-service/teleservice:0.4.3]()
  - **Triton 依赖版本**：[harbor.ctyuncdn.cn/ai-service/algorithm/triton-mask-detect:0.3.1]()
  - **性能测试**：
    - yolov5 性能测试（640x384）：
      - mask1.jpg（未佩戴口罩，1 人，1920 x 1080，375 KB）：**313.03 TPS**
      - mask4.jpg（口罩示范，4 人，975 x 300，90 KB）：**349.11 TPS**
    - yolov7 性能测试（640x640）：batch16
      - mask1.jpg（未佩戴口罩，1 人，1920 x 1080，375 KB）：**92.08 TPS**
      - mask4.jpg（口罩示范，4 人，975 x 300，90 KB）：**92.13 TPS**
    - yolov7 性能测试（384x640）：batch16
      - mask1.jpg（未佩戴口罩，1 人，1920 x 1080，375 KB）：**142.26 TPS**
      - mask4.jpg（口罩示范，4 人，975 x 300，90 KB）：**147.55 TPS**
  - **接口文档**：
    - https://www.yuque.com/linxiangpeng/qvlkz7/hi6gu7?singleDoc# 《边缘 AI-口罩检测-接口文档-v1.6》
    - https://www.yuque.com/linxiangpeng/qvlkz7/xvc5ef?singleDoc# 《诸葛 AI-口罩检测-接口文档-v1.5-官网》
    - https://www.yuque.com/linxiangpeng/qvlkz7/rd1gh7?singleDoc# 《温州 AI 中台-口罩检测-接口文档-v1.5》
    - https://www.yuque.com/linxiangpeng/qvlkz7/wppy7in5xahf87ov?singleDoc# 《边缘 AI-口罩检测-接口文档-v2.0》（新规范 NORM = new）
    - https://www.yuque.com/linxiangpeng/qvlkz7/noeli8nxrhvdsti2?singleDoc# 《天翼智慧城市-口罩检测-接口文档-v2.0》
  - **部署文档**：
    - https://www.yuque.com/linxiangpeng/qvlkz7/hbrk04?singleDoc# 《诸葛 AI-口罩检测-自测报告-0.1.0》
    - https://www.yuque.com/linxiangpeng/qvlkz7/yikmwi?singleDoc# 《诸葛 AI-口罩检测-部署文档-0.1.0》
    - https://www.yuque.com/linxiangpeng/qvlkz7/fn5259?singleDoc# 《温州 AI 中台-口罩检测-部署文档-0.1.0》
  - **测试文档**：
    - https://www.yuque.com/linxiangpeng/qvlkz7/hbrk04?singleDoc# 《诸葛 AI-口罩检测-自测报告-0.1.0》
  - **数据集**：
    - yolov5：0.2.0
      - 937 张测试图片（监控数据）：http://gz4oss.xstore.ctyun.cn/ocean/2022101217130588397676282_mask_images_937.zip（简略判断：97.9%，精确判断：93.5%）
      - 1000 张测试图片（网图）：http://gz4oss.xstore.ctyun.cn/ocean/20221019091932432711104385_mask_images_net_1000.zip（简略判断：99.5%，精确判断：97.4%）
    - yolov7：0.3.0（640x640 / 384x540）
      - 937 张测试图片（监控数据）：简略判断：97.8%(99.1%)，精确判断：92.8%(92.8%)
      - 1000 张测试图片（网图）：简略判断：98.9%(99.1%)，精确判断：97.4%(97%)
  - **更新时间**：2023.04.26

- **版本记录**：
  | 更新时间 | 版本号 | 修订内容 |
  | :-----: | :----: | :-----: |
  | 2022.08.11 | 0.1.5 | 基础版本 |
  | 2022.08.30 | 0.1.6 | 更新 teleservice:0.3.1，修复坐标负数 bug，增加保存规则的配置，更新 Action 字段错误，边缘节点上线版本 |
  | 2022.10.12 | 0.2.0 | 去掉 mask 分类模型和增加数据校验脚本以及数据挂载到容器 |
  | 2022.10.14 | 0.2.1 | 增加 MaskThresh，修改数据集计算方式 |
  | 2022.10.19 | 0.2.2 | 增加新数据集 1000 张，以及更新错误码 0.2.1 |
  | 2022.10.19 | 0.2.3 | 修复了错误码的 bug，更新错误码 0.2.2，脚本增加了 details 的判断 |
  | 2023.01.05 | 0.2.4 | 更新 teleservice:0.4.2，增加 content-type 错误码判断 |
  | 2023.02.17 | 0.2.5 | 框架 0.4.3，标准版本 |
  | 2023.03.27 | 0.3.0 | 同步数字生活场景，使用 yolov7 模型 |
  | 2023.04.23 | 0.3.1 | 接入 cicd |
  | 2023.04.26 | 0.3.2 | 镜像不变，部署增加保存图片和数据回流，注释掉保存规则 |
  | 2023.04.27 | 0.3.3 | 数据回流时，有结果即保存 |
