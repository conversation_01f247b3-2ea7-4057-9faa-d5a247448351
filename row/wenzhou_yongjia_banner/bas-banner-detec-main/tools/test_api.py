# -*- coding:utf-8 -*-
import json
import requests
import base64
from icecream import ic
import glob

# 服务调用脚本


def encode_base64_string(file_path):
    with open(file_path, "rb") as f:
        encoded_string = str(base64.urlsafe_b64encode(f.read()), "utf-8")
    return encoded_string


if __name__ == "__main__":

    app_key = "<EMAIL>"
    token = "MzIxNmFmOWU3MTViOTI4YzcyODVmMmYxYmE0ZjhkYjE6MTY2MTQ5NTAxOS42OTk2NjU1"

    target_url = 'http://bas-banner-detect-algorithm.ai.test.ctcdn.cn:9080/predict'
    # target_url = 'http://localhost:8099/predict'

    file_paths = glob.glob('../test/*.jpg')

    for idx, file_path in enumerate(file_paths):
        ic(idx, file_path)

        base64_string = encode_base64_string(file_path)
        headers = {"content-type": "application/json"}

        json_data = {
            "Action": "BannerDetect",
            "ImageData": base64_string,
            # "AppKey": app_key,
            # "Token": token,
            # "ScoreThresh": 0.5
        }

        resp_data = json.dumps(json_data)
        resp = requests.post(target_url,
                             data=resp_data,
                             headers=headers,
                             timeout=300)
        if resp.status_code != 200:
            print("Send fail.")
            ic(resp.status_code)
        else:
            json_data = json.loads(resp.text)
            if json_data.get("code") == 0:
                print("[SUCESS]")
                ic(json_data)
            else:
                print("[ERROR]")
                ic(json_data)
