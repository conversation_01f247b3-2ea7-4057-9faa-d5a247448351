import glob
import os
import sys
import time
from io import BytesIO
import gevent
import gevent.ssl
import numpy as np
import pybase64 as base64
import tritonclient.http as httpclient
from PIL import Image
import yaml
import cv2
import logging
import fleep
from teleexception import StatusException, HTTPStatus
from icecream import ic
from shapely.geometry import Polygon

logger = logging.getLogger(__name__)

CONFIG_YAML_PATH = "../../config.yaml"
LOCK_PATH = f"/tmp/init_logger"

with open(
    os.path.join(os.path.dirname(__file__), CONFIG_YAML_PATH), "r", encoding="utf-8"
) as f:
    configs = yaml.load(f, Loader=yaml.FullLoader)
    if not os.path.exists(LOCK_PATH) or not os.path.isdir(LOCK_PATH):
        os.makedirs(LOCK_PATH, exist_ok=True)
        logger.info(f"Init, {configs}")

action_configs = configs["action"]  # ACTION
port_configs = configs["ports"]  # 端口配置
models_configs = configs["models"]  # 模型参数配置
process_configs = configs["process"]  # 运行参数配置
params_configs = configs["params"]  # 系统参数配置

# 电动车端口
TRITON_URL = os.environ.get(
    "TRITON_URL", "localhost:{}".format(port_configs["TRITON_PORT"])
)

# 是否开启 ROI 区域
# enable 开启 roi，disable 关闭 roi，默认开启
ENABLE_ROI = os.environ.get("ENABLE_ROI", process_configs["ENABLE_ROI"])

# 电动车阈值 配置
CONFIG_EBIKE_THRESH = float(
    os.environ.get("EBIKE_THRESH", process_configs["EBIKE_THRESH"])
)

CONFIG_NMS_THRESH = float(
    os.environ.get("NMS_THRESH", process_configs["NMS_THRESH"])
)

# 图片格式 # default、tiff
IMAGE_FORMAT = os.environ.get("IMAGE_FORMAT", process_configs["IMAGE_FORMAT"])

# 电动车检测 模型配置 cv + trt
motorbike_plan_configs = models_configs["motorbike_plan"]


class DetectClient:
    def __init__(self, url, model_config):
        self.model_config = model_config
        try:
            if self.model_config["ssl"]:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url,
                    verbose=self.model_config["verbose"],
                    ssl=True,
                    ssl_context_factory=gevent.ssl._create_unverified_context,
                    insecure=True,
                )
            else:
                self.triton_client = httpclient.InferenceServerClient(
                    url=url, verbose=self.model_config["verbose"]
                )
        except Exception as e:
            logger.error("channel creation failed: " + str(e))
            sys.exit(1)
        if self.model_config["http_headers"] is not None:
            self.headers = {
                l.split(":")[0]: l.split(":")[1]
                for l in self.model_config["http_headers"]
            }
        else:
            self.headers = None
        self.model_name = self.model_config["model_name"]
        self.request_compression_algorithm = self.model_config[
            "request_compression_algorithm"
        ]
        self.response_compression_algorithm = self.model_config[
            "response_compression_algorithm"
        ]

    def run(self, input_data):
        inputs = [
            httpclient.InferInput(
                input_info["name"], input_data.shape, input_info["dtype"]
            )
            for input_info in self.model_config["input"]
        ]
        inputs[0].set_data_from_numpy(input_data, binary_data=True)

        outputs = [
            httpclient.InferRequestedOutput(output_info["name"], binary_data=True)
            for output_info in self.model_config["output"]
        ]
        query_params = {"test_1": 1, "test_2": 2}
        results = self.triton_client.async_infer(
            self.model_name,
            inputs,
            outputs=outputs,
            query_params=query_params,
            headers=self.headers,
            request_compression_algorithm=self.request_compression_algorithm,
            response_compression_algorithm=self.response_compression_algorithm,
        )
        return results


# 电动车检测 模型客户端 opencv + trt
motorbike_opencv_client = DetectClient(TRITON_URL, motorbike_plan_configs)


class EnsembleClient:
    def __init__(self, process_configs, reqid=None):
        self.reqid = reqid
        self.process_configs = process_configs
        self.save_rule = params_configs["SAVE_RULE"]  # 默认保存规则 all

    # opencv yolo预处理
    def letterbox(
        self,
        img,
        new_shape=(384, 640),
        color=(114, 114, 114),
        auto=False,
        scaleFill=False,
        scaleup=True,
        stride=32,
    ):
        # Resize and pad image while meeting stride-multiple constraints
        shape = img.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        # only scale down, do not scale up (for better test mAP)
        if not scaleup:
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = (
                new_shape[1] / shape[1],
                new_shape[0] / shape[0],
            )  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2

        if shape[::-1] != new_unpad:  # resize
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        img = cv2.copyMakeBorder(
            img, 0, bottom + top, 0, right + left, cv2.BORDER_CONSTANT, value=color
        )  # add border
        return img, ratio, (0, 0)

    # 电动车检测 opencv 预处理
    def _process_motorbike(self, input_image):
        img = self.letterbox(input_image)[0]

        # img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB
        img = img.transpose(2, 0, 1)  # 已经是 RGB

        img = np.ascontiguousarray(img)
        img = img.astype(np.float32)
        # img /= 255.0  # 0 - 255 to 0.0 - 1.0，根据模型训练而定

        if len(img) == 3:
            img = np.expand_dims(img, axis=0)

        return img

    def xywh2xyxy(self, x):
        # Convert nx4 boxes from [x, y, w, h] to [x1, y1, x2, y2] where xy1=top-left, xy2=bottom-right
        y = np.copy(x)
        y[:, 0] = x[:, 0] - x[:, 2] / 2  # top-left x
        y[:, 1] = x[:, 1] - x[:, 3] / 2  # top-left y
        y[:, 2] = x[:, 0] + x[:, 2] / 2  # bottom-right x
        y[:, 3] = x[:, 1] + x[:, 3] / 2  # bottom-right y
        return y

    def box_area(self, boxes):
        """
        :param boxes: [N, 4]
        :return: [N]
        """
        return (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])

    def box_iou(self, box1, box2):
        """
        :param box1: [N, 4]
        :param box2: [M, 4]
        :return: [N, M]
        """
        area1 = self.box_area(box1)  # N
        area2 = self.box_area(box2)  # M
        # broadcasting, 两个数组各维度大小 从后往前对比一致， 或者 有一维度值为1
        lt = np.maximum(box1[:, np.newaxis, :2], box2[:, :2])
        rb = np.minimum(box1[:, np.newaxis, 2:], box2[:, 2:])
        wh = rb - lt  # 右下角 - 左上角
        wh = np.maximum(0, wh)  # [N, M, 2]
        inter = wh[:, :, 0] * wh[:, :, 1]
        iou = inter / (area1[:, np.newaxis] + area2 - inter)
        return iou  # NxM

    def nms_numpy(self, boxes, scores, iou_threshold):

        idxs = scores.argsort()  # 按分数 降序排列的索引 [N]
        keep = []
        while idxs.size > 0:  # 统计数组中元素的个数
            max_score_index = idxs[-1]
            max_score_box = boxes[max_score_index][None, :]
            keep.append(max_score_index)

            if idxs.size == 1:
                break
            idxs = idxs[:-1]  # 将得分最大框 从索引中删除； 剩余索引对应的框 和 得分最大框 计算IoU；
            other_boxes = boxes[idxs]  # [?, 4]
            ious = self.box_iou(max_score_box, other_boxes)  # 一个框和其余框比较 1XM
            idxs = idxs[ious[0] <= iou_threshold]

        keep = np.array(keep)  # Tensor
        return keep

    def non_max_suppression(
        self, prediction, conf_thres=0.25, iou_thres=0.45, agnostic=False, max_det=100
    ):
        """Non-Maximum Suppression (NMS) on inference results to reject overlapping bounding boxes

        Returns:
            list of detections, on (n,6) tensor per image [xyxy, conf, cls]
        """
        if prediction.dtype is np.float16:
            prediction = prediction.astype(np.float32)  # to FP32
        xc = prediction[..., 4] > conf_thres  # candidates
        min_wh, max_wh = (
            params_configs["MIN_LEN"],
            params_configs["MAX_LEN"],
        )  # (pixels) minimum and maximum box width and height
        output = [None] * prediction.shape[0]

        for xi, x in enumerate(prediction):  # image index, image inference
            # Apply constraints
            # x[((x[..., 2:4] < min_wh) | (x[..., 2:4] > max_wh)).any(1), 4] = 0  # width-height
            x = x[xc[xi]]  # confidence

            # If none remain process next image
            if not x.shape[0]:
                continue
            # Compute conf
            x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf
            # Box (center x, center y, width, height) to (x1, y1, x2, y2)
            box = self.xywh2xyxy(x[:, :4])
            # Detections matrix nx6 (xyxy, conf, cls)
            conf, j = x[:, 5:].max(1, keepdims=True), x[:, 5:].argmax(
                1, keepdims=True
            ).astype(np.float32)
            x = np.concatenate((box, conf, j), axis=1)[conf.reshape(-1) > conf_thres]
            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            # Batched NMS
            c = x[:, 5:6] * (0 if agnostic else max_wh)  # classes
            boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores
            i = self.nms_numpy(boxes, scores, iou_thres)  # NMS
            if i.shape[0] > max_det:  # limit detections
                i = i[:max_det]
            output[xi] = x[i]

        return output

    def non_max_suppression_amd64(
        self, prediction, conf_thres=0.1, iou_thres=0.5, agnostic=False, max_det=100
    ):
        """Non-Maximum Suppression (NMS) on inference results to reject overlapping bounding boxes

        Returns:
            list of detections, on (n,6) tensor per image [xyxy, conf, cls]
        """
        if prediction.dtype is np.float16:
            prediction = prediction.astype(np.float32)  # to FP32
        xc = prediction[..., 4] > conf_thres  # candidates
        min_wh, max_wh = (
            params_configs["MIN_LEN"],
            params_configs["MAX_LEN"],
        )  # (pixels) minimum and maximum box width and height
        output = [None] * prediction.shape[0]

        for xi, x in enumerate(prediction):  # image index, image inference
            # Apply constraints
            # x[((x[..., 2:4] < min_wh) | (x[..., 2:4] > max_wh)).any(1), 4] = 0  # width-height
            x = x[xc[xi]]  # confidence

            # If none remain process next image
            if not x.shape[0]:
                continue
            # Compute conf
            # x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf
            # Box (center x, center y, width, height) to (x1, y1, x2, y2)
            box = x[:, :4]
            # Detections matrix nx6 (xyxy, conf, cls)
            conf, j = x[:, 4:5], x[:, 5:6]
            # conf, j = x[:, 5:].max(1, keepdims=True), x[:, 5:].argmax(1, keepdims=True).astype(np.float32)
            x = np.concatenate((box, conf, j), axis=1)[conf.reshape(-1) > conf_thres]
            # Check shape
            n = x.shape[0]  # number of boxes
            if not n:  # no boxes
                continue
            # Batched NMS
            c = x[:, 5:6] * (0 if agnostic else max_wh)  # classes
            boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores
            i = self.nms_numpy(boxes, scores, iou_thres)  # NMS
            if i.shape[0] > max_det:  # limit detections
                i = i[:max_det]
            output[xi] = x[i]

        return output

    # 电动车检测 后处理
    def _postprocess_motorbike(self, motorbike_detect_results, input_h, input_w):

        if False:

            motorbike_detect_results = self.non_max_suppression(
                motorbike_detect_results.as_numpy('output0'), agnostic=True
            )

            if motorbike_detect_results[0] is None:
                counts = 0
                boxes = []
                scores = []
                classes = []
            else:
                counts = len(motorbike_detect_results[0])
                boxes = motorbike_detect_results[0][:, :4]
                scores = motorbike_detect_results[0][:, 4]
                classes = motorbike_detect_results[0][:, 5]

            num_dets = np.array(counts)
            boxes = np.array(boxes[:num_dets])
            scores = np.array(scores[:num_dets])
            classes = np.array(classes[:num_dets])

        else:
            # print("motorbike_detect_results count")
            # print(motorbike_detect_results.as_numpy("count")[0])
            boxes = motorbike_detect_results.as_numpy("box")
            scores = motorbike_detect_results.as_numpy("score")[:, :, np.newaxis]
            classes = motorbike_detect_results.as_numpy("class")[:, :, np.newaxis]
            nms_input = np.concatenate((boxes, scores, classes), axis=2)

            motorbike_detect_results = self.non_max_suppression_amd64(
                nms_input, iou_thres=CONFIG_NMS_THRESH, agnostic=True
            )

            if motorbike_detect_results[0] is None:
                counts = 0
                boxes = []
                scores = []
                classes = []
            else:
                counts = len(motorbike_detect_results[0])
                boxes = motorbike_detect_results[0][:, :4]
                scores = motorbike_detect_results[0][:, 4]
                classes = motorbike_detect_results[0][:, 5]

            num_dets = np.array(counts)
            boxes = np.array(boxes[:num_dets])
            scores = np.array(scores[:num_dets])
            classes = np.array(classes[:num_dets])

        boxes = boxes.reshape((-1, 4))

        # counts = motorbike_detect_results.as_numpy("num_dets")[0]
        # boxes = motorbike_detect_results.as_numpy("det_boxes")[0]
        # scores = motorbike_detect_results.as_numpy("det_scores")[0]
        # classes = motorbike_detect_results.as_numpy("det_classes")[0]

        # num_dets = counts.squeeze()
        # boxes = boxes[:num_dets]
        # scores = scores[:num_dets]
        # classes = classes[:num_dets]

        ratio = (
            self.image_h / input_h
            if self.image_h / input_h > self.image_w / input_w
            else self.image_w / input_w
        )

        boxes *= ratio
        boxes = boxes.astype(np.int32)
        classes = classes.astype(np.int32)

        filter_boxes = []
        filter_scores = []
        filter_classes = []

        for i in range(len(classes)):
            # 类别过滤
            if classes[i] not in process_configs["KEEP_CLS_LIST"]:
                logger.info(
                    f"Reqid: {self.reqid}, motorbike is filtered by class: {classes[i]}"
                )
                continue

            # 阈值过滤
            if scores[i] < self.motorbike_thresh:
                logger.info(
                    f"Reqid: {self.reqid}, score: {scores[i]:.3f}, motorbike is filtered by thresh: {self.motorbike_thresh}"
                )
                continue

            # 检测框比例过滤
            box_w = (boxes[i][2] - boxes[i][0]) / self.image_w
            box_h = (boxes[i][3] - boxes[i][1]) / self.image_h
            min_w = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MIN_W_RATE"]
            max_w = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MAX_W_RATE"]
            min_h = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MIN_H_RATE"]
            max_h = process_configs["BOX_THRESH_RATE_DICT"]["BOX_MAX_H_RATE"]
            if not (min_w < box_w < max_w and min_h < box_h < max_h):
                logger.info(
                    f"Reqid: {self.reqid}, box is filtered by box rate, box_w: {box_w:.3f}, min_w: {min_w:.3f}, max_w: {max_w:.3f}, box_h: {box_h:.3f}, min_h: {min_h:.3f}, max_h: {max_h:.3f}"
                )
                continue

            filter_boxes.append(boxes[i])
            filter_scores.append(scores[i])
            filter_classes.append(classes[i])

        return filter_boxes, filter_scores, filter_classes

    def _batch(self, iterable, n=1):
        l = len(iterable)
        for ndx in range(0, l, n):
            yield iterable[ndx : min(ndx + n, l)]

    # 计算两个多边形是否有交集
    @staticmethod
    def get_polygon_inter(points1, points2):
        """
        get intersection between polygon;
        :param points1: base points eg. [(344,362),(767,370),(731,698),(274,691)]
        :param points2: targets, eg. [235, 257, 873, 720]
        :return:
        """
        polygon = Polygon(points1)
        x1, y1, x2, y2 = points2
        target = Polygon([(x1, y1), (x2, y1), (x2, y2), (x1, y2)]).buffer(0.001)
        intersection = polygon.intersection(target)
        return intersection.area > 0

    # 返回值封装
    def update_final_result(self, boxes, scores, classes, features=[]):

        fix_boxes = []
        fix_scores = []
        fix_classes = []

        cls_map = process_configs["CLS_MAP"]

        for value in zip(boxes, scores, classes):
            box, score, cls = value[0], value[1], value[2]

            if ENABLE_ROI == "enable":  # 开启 ROI 判断
                # 计算是否满足ROI区域
                # [[0, 0], [0, 50], [50, 0], [50, 50]] 非凸多边形
                try:
                    state = self.get_polygon_inter(self.Roi, box)
                except Exception:
                    logger.info(
                        f"Reqid: {self.reqid}, R0I not polygon, return code: 602001"
                    )
                    raise StatusException(HTTPStatus.ROI_POLYGON)

                if state is False:  # 无交集，过滤
                    continue

            # 避免越界
            x1 = int(np.clip(box[0], 0, self.image_w))
            y1 = int(np.clip(box[1], 0, self.image_h))
            x2 = int(np.clip(box[2], 0, self.image_w))
            y2 = int(np.clip(box[3], 0, self.image_h))
            new_box = [x1, y1, x2 - x1, y2 - y1]  # x, y, w, h

            new_score = round(float(score), 4)
            new_cls = cls_map[int(cls)]

            fix_boxes.append(new_box)
            fix_scores.append(new_score)
            fix_classes.append(new_cls)

        final_result = {
            "DetectCount": len(fix_boxes),
            "DetectBoxes": fix_boxes,
            "DetectScores": fix_scores,
            "DetectClses": fix_classes,
        }

        # fix_boxes = []
        # for box in boxes:
        #     # 避免越界
        #     x1 = int(np.clip(box[0], 0, self.image_w))
        #     y1 = int(np.clip(box[1], 0, self.image_h))
        #     x2 = int(np.clip(box[2], 0, self.image_w))
        #     y2 = int(np.clip(box[3], 0, self.image_h))
        #     # x1, y1, x2, y2 = [int(v) if int(v) > 0 else 0 for v in box]
        #     new_box = [x1, y1, x2 - x1, y2 - y1]  # x, y, w, h
        #     fix_boxes.append(new_box)

        # fix_scores = [round(float(score), 4) for score in scores]

        # cls_map = process_configs["CLS_MAP"]
        # fix_classes = [cls_map[int(cls)] for cls in classes]

        # final_result = {
        #     "DetectCount": len(boxes),
        #     "DetectBoxes": fix_boxes,
        #     "DetectScores": fix_scores,
        #     "DetectClses": fix_classes,
        # }

        # 检测框 <= 2 不保存
        if len(boxes) <= params_configs["SAVE_NUM"]:
            self.save_rule = "none"

        logger.info(
            f"Reqid: {self.reqid}, DetectCount:{len(boxes)}, SaveRule: {self.save_rule}"
        )

        return final_result, self.save_rule

    def run(self, img_arr, img_mat, motorbike_thresh=CONFIG_EBIKE_THRESH, Roi=None):
        # 参数初始化
        self.image_h, self.image_w = img_mat.shape[:2]  # 图片长宽 opencv
        self.motorbike_thresh = motorbike_thresh  # 电动车阈值
        self.Roi = Roi
        logger.info(
            f"Reqid: {self.reqid}, image_h: {self.image_h}, image_w: {self.image_w}, motorbike_thresh: {motorbike_thresh}, Roi: {self.Roi}"
        )

        # 模型推理
        input_data = self._process_motorbike(img_mat)  # RGB
        motorbike_results = motorbike_opencv_client.run(input_data)
        motorbike_detect_results = motorbike_results.get_result()
        # 后处理
        boxes, scores, classes = self._postprocess_motorbike(
            motorbike_detect_results,
            motorbike_plan_configs["input_shape"][0]["input_h"],
            motorbike_plan_configs["input_shape"][0]["input_w"],
        )

        # 未检测到电动车
        if len(boxes) == 0:
            logger.info(f"Reqid: {self.reqid}, no detect motorbike")

        return self.update_final_result(boxes, scores, classes)


class FileFormatValidation:
    @staticmethod
    def validate(file: bytes, mime_matches):
        if file is None or len(file) <= 128:
            return False

        info = fleep.get(file[:128])
        for mime in mime_matches:
            if info.mime_matches(mime):
                return True
        return False

    @staticmethod
    def convert_to_png(self):
        im = Image.open(BytesIO(self.file))
        byte_io = BytesIO()
        im.save(byte_io, "PNG")
        self.cleaned_image = byte_io.getvalue()


def check_params(reqid, params):
    # 请求体为字典
    if not isinstance(params, dict):
        logger.info(f"Reqid: {reqid}, body type err, return code: 400005")
        raise StatusException(HTTPStatus.BODY_TYPE_ERR)

    # Action and ImageData
    Action = params.get("Action", None)
    ImageData = params.get("ImageData", None)

    if Action is None or ImageData is None:
        logger.info(f"Reqid: {reqid}, Action or ImageData missing, return code: 400006")
        raise StatusException(HTTPStatus.MUST_PRAM_ERR)
    if not isinstance(Action, str) or not isinstance(ImageData, str):
        logger.info(
            f"Reqid: {reqid}, Action or ImageData type err, return code: 400008"
        )
        raise StatusException(HTTPStatus.PRAM_TYPE_ERR)
    if not Action or not ImageData:
        logger.info(f"Reqid: {reqid}, Action or ImageData empty, return code: 400009")
        raise StatusException(HTTPStatus.IMAGE_DATA_AND_ACTION_EMPTY_ERR)
    if Action != action_configs:  # ACTION
        logger.info(f"Reqid: {reqid}, Action value err, return code: 400010")
        raise StatusException(HTTPStatus.ACTION_VALUE_ERR)

    # base 解码
    try:
        img_byte = base64.urlsafe_b64decode(ImageData)
    except Exception as e:
        logger.info(
            f"Reqid: {reqid}, decode image base64 err: {e}, return code: 400011"
        )
        raise StatusException(HTTPStatus.IMAGE_DATA_BASE64_ERR)

    # 图片格式
    if IMAGE_FORMAT == "default":  # 默认格式
        if not FileFormatValidation.validate(img_byte, params_configs["FILE_FORMAT"]):
            logger.info(f"Reqid: {reqid}, image format err, return code: 400012")
            raise StatusException(HTTPStatus.IMAGE_TYPE_ERR)
    elif IMAGE_FORMAT == "tiff":  # 支持 tiff 格式
        if not FileFormatValidation.validate(
            img_byte, params_configs["FILE_FORMAT_TIFF"]
        ):
            logger.info(f"Reqid: {reqid}, image format err, return code: 400012")
            raise StatusException(HTTPStatus.IMAGE_TYPE_TIFF_ERR)

    # 图片大小
    if len(img_byte) > params_configs["IMAGE_SIZE"]:
        logger.info(f"Reqid: {reqid}, image size err, return code: 400013")
        raise StatusException(HTTPStatus.IMAGE_SIZE_ERR)

    # 图片解码
    try:
        img_arr = np.frombuffer(img_byte, np.uint8)
        img_mat = cv2.cvtColor(
            cv2.imdecode(img_arr, cv2.IMREAD_COLOR), cv2.COLOR_BGR2RGB
        )
        height, width = img_mat.shape[:2]
    except Exception as e:
        logger.info(f"Reqid: {reqid}, decode image err: {e}, return code: 410001")
        raise StatusException(HTTPStatus.IMAGE_DECODE_ERR)

    # 图片长宽
    if not (
        params_configs["MIN_LEN"] <= height <= params_configs["MAX_LEN"]
        and params_configs["MIN_LEN"] <= width <= params_configs["MAX_LEN"]
    ):
        logger.info(f"Reqid: {reqid}, image shape err, return code: 410002")
        raise StatusException(HTTPStatus.IMAGE_SHAPE_ERR)

    # Roi 区域判断
    if ENABLE_ROI == "enable":  # 开启 ROI 判断
        # 默认全图
        Roi = params.get("Roi", [[0, 0], [0, height], [width, height], [width, 0]])
        logger.info(f"Reqid: {reqid}, Roi: {Roi}")

        if not isinstance(Roi, list):
            ic(type(Roi))
            logger.info(f"Reqid: {reqid}, Roi type err, return code: 400008")
            raise StatusException(HTTPStatus.ROI_LIST_TYPE_ERR)
        for item in Roi:
            if len(item) == 2:
                if not isinstance(item[0], int) or not isinstance(item[1], int):
                    logger.info(
                        f"Reqid: {reqid}, Roi item type err, return code: 400008"
                    )
                    raise StatusException(HTTPStatus.ROI_ITEM_INT_TYPE_ERR)
            else:
                logger.info(f"Reqid: {reqid}, Roi item len err, return code: 400008")
                raise StatusException(HTTPStatus.ROI_ITEM_INT_TYPE_ERR)
        if not 3 < len(Roi) < 10:
            logger.info(
                f"Reqid: {reqid}, Roi lenghth value more than 3 but less than 10 , return code: 400019"
            )
            raise StatusException(HTTPStatus.ROI_LENGTH_ERR)
    else:
        # 默认全图
        Roi = [[0, 0], [0, height], [width, height], [width, 0]]
        logger.info(f"Reqid: {reqid}, Roi: {Roi}")

    # 业务字段 电动车阈值
    motorbike_thresh = params.get("EbikeThresh", CONFIG_EBIKE_THRESH)
    if not (isinstance(motorbike_thresh, int) or isinstance(motorbike_thresh, float)):
        logger.info(
            f"Reqid: {reqid}, EbikeThresh data type is illegal, return code: 400008"
        )
        raise StatusException(HTTPStatus.EBIKE_THRESH_FLOAT_TYPE_ERR)
    if not 0 <= motorbike_thresh <= 1:
        logger.info(f"Reqid: {reqid}, EbikeThresh data is illegal, return code: 400010")
        raise StatusException(HTTPStatus.EBIKE_THRESH_VALUE_ERR)

    return img_arr, img_mat, motorbike_thresh, Roi


def run(reqid, params):
    # 参数校验及初始化
    time_0 = time.time()
    img_arr, img_mat, motorbike_thresh, Roi = check_params(reqid, params)
    client = EnsembleClient(process_configs, reqid)

    # 推理执行
    time_1 = time.time()
    results, save_rule = client.run(img_arr, img_mat, motorbike_thresh, Roi)
    time_2 = time.time()

    logger.info(
        f"Reqid: {reqid}, check params: {(time_1 - time_0) * 1000} ms, infer time: {(time_2 - time_1) * 1000} ms"
    )

    return results, save_rule


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s %(levelname)s %(filename)s:%(lineno)d - %(message)s",
    )

    client = EnsembleClient(process_configs)

    image_paths = glob.glob("../../test/test_ebike_elevator/*.jpg")

    for idx, image_path in enumerate(image_paths):
        with open(image_path, "rb") as file:
            img_arr = np.frombuffer(file.read(), np.uint8)

        img_mat = cv2.cvtColor(
            cv2.imdecode(img_arr, cv2.IMREAD_COLOR), cv2.COLOR_BGR2RGB
        )
        img_h, img_w = img_mat.shape[:2]
        results, _ = client.run(
            img_arr, img_mat, Roi=[[0, 0], [0, img_h], [img_w, img_h], [img_w, 0]]
        )
        ic(idx, image_path, results)
