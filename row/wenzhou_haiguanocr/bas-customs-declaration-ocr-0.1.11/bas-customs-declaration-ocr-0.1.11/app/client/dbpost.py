import cv2
import numpy as np
import pyclipper
from shapely.geometry import Polygon


class dataset_sort:
    def __init__(self, a, i):
        self.data = a
        self.ind = i


class DBPostProcess(object):
    """
    The post process for Differentiable Binarization (DB).
    """

    def __init__(self, params):
        self.thresh = params["thresh"]
        self.box_thresh = params["box_thresh"]
        self.max_candidates = params["max_candidates"]
        self.unclip_ratio = params["unclip_ratio"]
        # self.min_size = 3
        self.min_size = params["min_size"]

    def boxes_from_bitmap(self, pred, _bitmap, dest_width, dest_height):
        """
        _bitmap: single map with shape (1, H, W),
                whose values are binarized as {0, 1}
        """
        bitmap = _bitmap
        height, width = bitmap.shape
        # kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        bitmap = (bitmap * 255).astype(np.uint8)
        # bitmap = cv2.dilate(bitmap, kernel)
        # cv2.imwrite('/data/guoyihao/project/idcard/text_recog/cllient/bitmap_dilate.jpg', bitmap) # 'bitmap.jpg'
        outs = cv2.findContours(bitmap, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        # outs = cv2.findContours((bitmap * 255).astype(np.uint8), cv2.RETR_LIST,
        #                         cv2.CHAIN_APPROX_SIMPLE)
        if len(outs) == 3:
            img, contours, _ = outs[0], outs[1], outs[2]
        elif len(outs) == 2:
            contours, _ = outs[0], outs[1]

        num_contours = min(len(contours), self.max_candidates)
        boxes = np.zeros((num_contours, 4, 2), dtype=np.float32)
        scores = np.zeros((num_contours,), dtype=np.float32)

        for index in range(num_contours):
            contour = contours[index]
            points, sside = self.get_mini_boxes(contour)
            if sside < self.min_size:
                continue
            points = np.array(points)
            score = self.box_score_fast(pred, points.reshape(-1, 2))
            if self.box_thresh > score:
                continue

            box = self.unclip(points).reshape(-1, 1, 2)
            box, sside = self.get_mini_boxes(box)
            if sside < self.min_size + 2:
                continue
            box = np.array(box)
            if not isinstance(dest_width, int):
                dest_width = dest_width.item()
                dest_height = dest_height.item()

            box[:, 0] = np.clip(np.round(box[:, 0] / width * dest_width), 0, dest_width)
            box[:, 1] = np.clip(
                np.round(box[:, 1] / height * dest_height), 0, dest_height
            )
            # boxes[index, :, :] = box.astype(np.int16)
            boxes[index, :, :] = box.astype(np.float32)
            scores[index] = score
        return boxes, scores

    def unclip(self, box):
        unclip_ratio = self.unclip_ratio
        poly = Polygon(box)
        distance = poly.area * unclip_ratio / poly.length
        offset = pyclipper.PyclipperOffset()
        offset.AddPath(box, pyclipper.JT_ROUND, pyclipper.ET_CLOSEDPOLYGON)
        expanded = np.array(offset.Execute(distance))
        return expanded

    def get_mini_boxes(self, contour):
        bounding_box = cv2.minAreaRect(contour)
        # 针对opencv4.5做出修改
        bounding_box_list = []
        bounding_box0_list = list(bounding_box[0])
        bounding_box_list.append(bounding_box0_list)
        bounding_box1_list = list(bounding_box[1])
        bounding_box_list.append(bounding_box1_list)
        bounding_box_list.append(bounding_box[2])
        if bounding_box_list[2] == 90:
            bounding_box_list[2] = -90
        elif bounding_box_list[2] > 0 and bounding_box_list[2] < 90:
            bounding_box_list[2] = bounding_box_list[2] - 90
            tmp = bounding_box_list[1][0]
            bounding_box_list[1][0] = bounding_box_list[1][1]
            bounding_box_list[1][1] = tmp
        #
        points = sorted(list(cv2.boxPoints(bounding_box)), key=lambda x: x[0])

        index_1, index_2, index_3, index_4 = 0, 1, 2, 3
        if points[1][1] > points[0][1]:
            index_1 = 0
            index_4 = 1
        else:
            index_1 = 1
            index_4 = 0
        if points[3][1] > points[2][1]:
            index_2 = 2
            index_3 = 3
        else:
            index_2 = 3
            index_3 = 2

        box = [points[index_1], points[index_2], points[index_3], points[index_4]]
        cv2_version = cv2.__version__
        val_version = float(cv2_version[0:3])
        if val_version >= 4.5:
            # opencv4.5
            # return box, bounding_box_list
            return box, min(bounding_box_list[1])
        else:
            # opencv4.4
            # return box, bounding_box
            return box, min(bounding_box[1])

    def box_score_fast(self, bitmap, _box):
        h, w = bitmap.shape[:2]
        box = _box.copy()
        xmin = np.clip(np.floor(box[:, 0].min()).astype(np.int32), 0, w - 1)
        xmax = np.clip(np.ceil(box[:, 0].max()).astype(np.int32), 0, w - 1)
        ymin = np.clip(np.floor(box[:, 1].min()).astype(np.int32), 0, h - 1)
        ymax = np.clip(np.ceil(box[:, 1].max()).astype(np.int32), 0, h - 1)

        mask = np.zeros((ymax - ymin + 1, xmax - xmin + 1), dtype=np.uint8)
        box[:, 0] = box[:, 0] - xmin
        box[:, 1] = box[:, 1] - ymin
        cv2.fillPoly(mask, box.reshape(1, -1, 2).astype(np.int32), 1)
        return cv2.mean(bitmap[ymin : ymax + 1, xmin : xmax + 1], mask)[0]

    def __call__(self, pred, dbnet_input_h, dbnet_input_w, image_h, image_w):
        pred = pred[0, :, :]
        segmentation = pred > self.thresh
        tmp_boxes, tmp_scores = self.boxes_from_bitmap(
            pred, segmentation, dbnet_input_w, dbnet_input_h
        )
        ratio = (
            image_h / dbnet_input_h
            if image_h / dbnet_input_h > image_w / dbnet_input_w
            else image_w / dbnet_input_w
        )
        tmp_boxes *= ratio
        tmp_boxes = tmp_boxes.astype(np.int32)
        boxes = []
        scores = []
        for k in range(len(tmp_boxes)):
            if tmp_scores[k] > self.box_thresh:
                boxes.append(tmp_boxes[k])
                scores.append(tmp_scores[k])
        if len(boxes) > 0:
            boxes = np.array(boxes)
            scores = np.array(scores)
        # 排序
        dataset_a = []
        for i in range(len(boxes)):
            dataset_a.append(dataset_sort(boxes[i], i))
        dataset_b = sorted(dataset_a, key=lambda x: (x.data[0][1], x.data[0][0]))
        for i in range(len(dataset_b) - 1):
            if abs(dataset_b[i + 1].data[0][1] - dataset_b[i].data[0][1]) < 10 and (
                dataset_b[i + 1].data[0][0] < dataset_b[i].data[0][0]
            ):
                tmp = dataset_b[i]
                dataset_b[i] = dataset_b[i + 1]
                dataset_b[i + 1] = tmp
        boxes_sorted = []
        scores_sorted = []
        for i in range(len(dataset_b)):
            ind = dataset_b[i].ind
            boxes_sorted.append(boxes[ind])
            scores_sorted.append(scores[ind])
        #
        return boxes_sorted, scores_sorted

    def __repr__(self):
        return (
            self.__class__.__name__
            + " thresh: {1}, box_thresh: {2}, max_candidates: {3}, unclip_ratio: {4}, min_size: {5}".format(
                self.thresh,
                self.box_thresh,
                self.max_candidates,
                self.unclip_ratio,
                self.min_size,
            )
        )
