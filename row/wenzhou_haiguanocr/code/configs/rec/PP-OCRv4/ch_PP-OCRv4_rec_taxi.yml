Global:
  debug: false
  use_gpu: true
  epoch_num: 200
  log_smooth_window: 20
  print_batch_step: 100
  save_model_dir: ./output/rec_ppocr_v4_taxi_h48_1114
  save_epoch_step: 40
  eval_batch_step:
  - 0
  - 4000
  cal_metric_during_train: true
  pretrained_model: null
  checkpoints: null
  save_inference_dir: null
  use_visualdl: false
  infer_img: doc/imgs_words/ch/word_1.jpg
  character_dict_path: ppocr/utils/ppocr_keys_v1.txt
  max_text_length: &max_text_length 30
  infer_mode: false
  use_space_char: true
  distributed: true
  save_res_path: ./output/rec/predicts_ppocrv4_taxinote0130.txt

Optimizer:
  name: Adam
  beta1: 0.9
  beta2: 0.999
  lr:
    name: Cosine
    learning_rate: 0.001
    warmup_epoch: 5
  regularizer:
    name: L2
    factor: 3.0e-05


Architecture:
  model_type: rec
  algorithm: SVTR_LCNet
  Transform:
  Backbone:
    name: PPLCNetV3
    scale: 0.95
  Head:
    name: MultiHead
    head_list:
      - CTCHead:
          Neck:
            name: svtr
            dims: 120
            depth: 2
            hidden_dims: 120
            kernel_size: [1, 3]
            use_guide: True
          Head:
            fc_decay: 0.00001
      - NRTRHead:
          nrtr_dim: 384
          max_text_length: *max_text_length

Loss:
  name: MultiLoss
  loss_config_list:
    - CTCLoss:
    - NRTRLoss:

PostProcess:  
  name: CTCLabelDecode

Metric:
  name: RecMetric
  main_indicator: acc


Train:
  dataset:
    name: MultiScaleDataSet
    ds_width: false
    data_dir: /data2/qianxiang/qx/crnn_img/train_dataset/rec/train/
    ext_op_transform_idx: 1
    label_file_list:
    - /data2/qianxiang/qx/crnn_img/train_dataset/gt_train_lable_all.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/gt_train_fapiao_0719.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/label_verify_0807_train_X10.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/taxi_pp3_pp4server_same_train_0831_X20.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/ccf_taxi_data_train_0906_X20.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/taxi_other_0907_X20.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/taxi_20230913_pp3_pp4server_same_train_X20.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/taxi_data_20230913_other_d_X20.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/taxi_ticket_0_45_partimg_label_X20.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/croped_taxi_note_partimg_digit_label_train_1103_X10.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/croped_taxi_note_partimg_digit_label_train_diff_1103_X10.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/StyleText_taxinote_40w_1103_label.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/taxinote_test_200_partimg_train_1110_X5.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/taxinote_test_200_partimg_train_diff_1110_X20.txt
    ratio_list: [0.1, 0.5, 1, 1, 1, 1, 1, 1, 0.2, 0, 0, 0, 1, 1]
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - RecConAug:
        prob: 0.5
        ext_data_num: 2
        image_shape: [48, 320, 3]
        max_text_length: *max_text_length
    - RecAug:
    - MultiLabelEncode:
        gtc_encode: NRTRLabelEncode
    - KeepKeys:
        keep_keys:
        - image
        - label_ctc
        - label_gtc
        - length
        - valid_ratio
  sampler:
    name: MultiScaleSampler
    scales: [[320, 32], [320, 48], [320, 64]]
    first_bs: &bs 96
    fix_bs: false
    divided_factor: [8, 16] # w, h
    is_training: True
  loader:
    shuffle: true
    batch_size_per_card: *bs
    drop_last: true
    num_workers: 8


Eval:
  dataset:
    name: SimpleDataSet
    data_dir: /data2/qianxiang/qx/crnn_img/train_dataset/rec/test/
    label_file_list:
    - /data2/qianxiang/qx/crnn_img/train_dataset/gt_test_lable_all.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/gt_test_fapiao_0719.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/label_verify_0807_test.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/taxi_pp3_pp4server_same_test_0907.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/ccf_taxi_data_test_0905.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/taxi_20230913_pp3_pp4server_same_test.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/croped_taxi_note_partimg_digit_label_test_1103.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/croped_taxi_note_partimg_digit_label_test_diff_1103.txt
    - /data2/qianxiang/qx/crnn_img/train_dataset/taxinote_test_200_partimg_label_test_1110.txt
    ratio_list: [0.000, 0.00, 0.00, 1, 1, 1, 0.1, 0.1, 1]
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - MultiLabelEncode:
        gtc_encode: NRTRLabelEncode
    - RecResizeImg:
        image_shape: [3, 48, 320]
    - KeepKeys:
        keep_keys:
        - image
        - label_ctc
        - label_gtc
        - length
        - valid_ratio
  loader:
    shuffle: false
    drop_last: false
    batch_size_per_card: 64
    num_workers: 4

