@Library('cdn-devops@multi_branch') _

def RELEASE_BUILD
String BUILD_RESULT = ""

pipeline {
    agent {
        label 'jnlp-slave'
    }
	options {
		buildDiscarder(logRotator(numToKeepStr: '10'))
		disableConcurrentBuilds()
		skipDefaultCheckout()
		timeout(time: 30, unit: 'MINUTES')
		gitLabConnection('gitlab')
	}
    environment {
        IMAGE_CREDENTIALS = "credential-harbor"
        // 镜像仓库地址
        IMAGE_REPOSITORY = "harbor.ctyuncdn.cn/ai-service/algorithm/bas-traffic-detect"

        //  定义名字
        DEPLOYMENT_NAME = "bas-traffic-detect"
        SERVICE_TARGET_PORT = 80          // 程序启用的端口号

        DEV_BRANCH = ""
        // 执行条件（通用）
        QA_BRANCH = "\\d+\\.\\d+.\\d+(-\\d+)?" // 
        // 配置文件的路径
        DEPLOY_BASE_DIR = "deploy"
        DEV_NAMESPACE = "algorithm"
        QA_NAMESPACE = "algorithm"
        KUBECONFIG_DEV = "/root/.kube/kube-config-bgai-dev/kube-config-bgai-dev"
        KUBECONFIG_QA = "/root/.kube/kube-config-bgai/kube-config-bgai"
    }

    stages {
        stage('Checkout') {
            steps {
                script {
                    container('tools') {
                        // checkout code
                        retry(2) { scmVars = checkout scm }
                        RELEASE_BUILD = scmVars.GIT_COMMIT
                        BUILD_RESULT = devops.updateBuildTasks(BUILD_RESULT,"Checkout OK...  √")
                        echo 'begin checkout...'
                        echo sh(returnStdout: true, script: "env")
                    }
                }
            }
        }
        stage('CI'){
            failFast true
            parallel {
                stage('Unit Test') {
                    when {
                        expression { BRANCH_NAME ==~ env.DEV_BRANCH || TAG_NAME ==~ env.QA_BRANCH}
                    }
                    steps {
                        script {
                            container('tools') {
                                echo 'skip unit test'
                            }
                        }
                    }
                }
                stage('Code Scan') {
                    when {
                        expression { BRANCH_NAME ==~ env.DEV_BRANCH || TAG_NAME ==~ env.QA_BRANCH }
                    }
                    steps {
                        script {
                            container('tools') {
                                echo 'skip'
                                //devops.scan().start()
                            }
                        }
                    }
                }
            }
        }

        stage('Build-Image') {
            when {
                expression { BRANCH_NAME ==~ env.DEV_BRANCH || TAG_NAME ==~ env.QA_BRANCH }
            }
            steps {
                script {
                    container('ecx-docker-with-buildx') {
                        devops.dockerBuild(
                            "Dockerfile", //Dockerfile
                            ".", // build context
                            IMAGE_REPOSITORY, // repo address
                            RELEASE_BUILD, // tag
                            IMAGE_CREDENTIALS, // credentials for pushing
                        ).start().push()
                    }
                }
            }
        }

        stage('Deploy') {
            when {
                expression { BRANCH_NAME ==~ env.DEV_BRANCH || TAG_NAME ==~ env.QA_BRANCH }
            }

            steps {
                script {
                    // 替换占位，不能直接用环境变量获取
                    sh """
                        sed -i 's#{{DEPLOYMENT_NAME}}#'$DEPLOYMENT_NAME'#g' ${DEPLOY_BASE_DIR}/deployment.yaml
                        sed -i 's#{{SERVICE_TARGET_PORT}}#'$SERVICE_TARGET_PORT'#g' ${DEPLOY_BASE_DIR}/deployment.yaml
                    """
                    container('tools') {
                        // create configmap and ingress
                        // devops.deploy("", "${DEPLOY_VIP_Dcc.cipIR}/ingress.yaml","",false).start()
                        dep = devops.deploy(
                            "${DEPLOY_BASE_DIR}", //k8s files dir
                            "${DEPLOY_BASE_DIR}/deployment.yaml",
                            RELEASE_BUILD,
                            false
                        )
                        dep.start()
                    }
                }
            }
        }
    }

    post {
        success {
            script {
                container('tools') {
                    devops.notificationSuccess(DEPLOYMENT_NAME, "流水线完成了", RELEASE_BUILD, "wechat-bot-bigdataai")
                }
            }
        }
        failure {
            script {
                container('tools') {
                    devops.notificationFailed(DEPLOYMENT_NAME, "流水线失败了", RELEASE_BUILD, "wechat-bot-bigdataai")
                }
            }
        }
    }
}

