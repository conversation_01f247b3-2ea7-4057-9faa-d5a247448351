"""
视频处理工具模块
提供视频处理相关的工具函数
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Generator, Tuple
import asyncio
import time
from pathlib import Path
from loguru import logger


class VideoProcessor:
    """视频处理器"""
    
    def __init__(self):
        self.cap = None
        self.writer = None
        self.frame_count = 0
        self.fps = 30.0
        self.frame_width = 1920
        self.frame_height = 1080
        
    def open_video(self, video_path: str) -> bool:
        """
        打开视频文件或摄像头
        
        Args:
            video_path: 视频文件路径或摄像头索引
            
        Returns:
            是否成功打开
        """
        try:
            # 尝试作为摄像头索引
            if video_path.isdigit():
                self.cap = cv2.VideoCapture(int(video_path))
            else:
                self.cap = cv2.VideoCapture(video_path)
            
            if not self.cap.isOpened():
                logger.error(f"无法打开视频: {video_path}")
                return False
            
            # 获取视频属性
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.frame_count = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            logger.info(f"视频打开成功: {video_path}")
            logger.info(f"分辨率: {self.frame_width}x{self.frame_height}")
            logger.info(f"帧率: {self.fps}")
            logger.info(f"总帧数: {self.frame_count}")
            
            return True
            
        except Exception as e:
            logger.error(f"打开视频失败: {e}")
            return False
    
    def read_frame(self) -> Optional[np.ndarray]:
        """
        读取一帧
        
        Returns:
            图像帧或None
        """
        if self.cap is None:
            return None
        
        ret, frame = self.cap.read()
        if ret:
            return frame
        return None
    
    def frame_generator(self) -> Generator[np.ndarray, None, None]:
        """
        帧生成器
        
        Yields:
            视频帧
        """
        while True:
            frame = self.read_frame()
            if frame is None:
                break
            yield frame
    
    async def process_video_async(
        self, 
        video_path: str, 
        processor_func, 
        output_path: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        异步处理视频
        
        Args:
            video_path: 输入视频路径
            processor_func: 处理函数
            output_path: 输出视频路径
            
        Returns:
            处理结果列表
        """
        if not self.open_video(video_path):
            return []
        
        results = []
        frame_index = 0
        
        # 初始化输出视频写入器
        if output_path:
            self._init_video_writer(output_path)
        
        try:
            for frame in self.frame_generator():
                # 异步处理帧
                result = await processor_func(frame, frame_index)
                results.append(result)
                
                # 写入输出视频
                if self.writer and 'processed_frame' in result:
                    self.writer.write(result['processed_frame'])
                
                frame_index += 1
                
                # 让出控制权
                if frame_index % 10 == 0:
                    await asyncio.sleep(0.001)
                
        except Exception as e:
            logger.error(f"视频处理过程中发生错误: {e}")
        
        finally:
            self.close()
        
        return results
    
    def _init_video_writer(self, output_path: str) -> None:
        """初始化视频写入器"""
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        self.writer = cv2.VideoWriter(
            output_path, 
            fourcc, 
            self.fps, 
            (self.frame_width, self.frame_height)
        )
    
    def close(self) -> None:
        """关闭视频处理器"""
        if self.cap:
            self.cap.release()
            self.cap = None
        
        if self.writer:
            self.writer.release()
            self.writer = None
    
    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频信息
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频信息字典
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            return {}
        
        info = {
            'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'fps': cap.get(cv2.CAP_PROP_FPS),
            'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'duration': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS)
        }
        
        cap.release()
        return info
    
    @staticmethod
    def extract_frames(
        video_path: str, 
        output_dir: str, 
        interval: int = 30,
        max_frames: int = 100
    ) -> List[str]:
        """
        从视频中提取帧
        
        Args:
            video_path: 视频文件路径
            output_dir: 输出目录
            interval: 提取间隔（帧数）
            max_frames: 最大提取帧数
            
        Returns:
            提取的帧文件路径列表
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            logger.error(f"无法打开视频: {video_path}")
            return []
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        frame_paths = []
        frame_index = 0
        saved_count = 0
        
        while saved_count < max_frames:
            ret, frame = cap.read()
            if not ret:
                break
            
            if frame_index % interval == 0:
                frame_filename = f"frame_{frame_index:06d}.jpg"
                frame_path = output_path / frame_filename
                
                cv2.imwrite(str(frame_path), frame)
                frame_paths.append(str(frame_path))
                saved_count += 1
            
            frame_index += 1
        
        cap.release()
        logger.info(f"从视频中提取了 {saved_count} 帧")
        return frame_paths


class VideoStabilizer:
    """视频稳定器（基于原始fangdou.cpp的Python实现）"""
    
    def __init__(self, smoothing_radius: int = 15):
        self.smoothing_radius = smoothing_radius
        self.transforms = []
        self.trajectory = []
        self.smoothed_trajectory = []
        
    def stabilize_video(self, input_path: str, output_path: str) -> bool:
        """
        稳定视频
        
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            
        Returns:
            是否成功
        """
        try:
            # 第一步：计算帧间变换
            if not self._calculate_transforms(input_path):
                return False
            
            # 第二步：平滑轨迹
            self._smooth_trajectory()
            
            # 第三步：应用稳定变换
            return self._apply_stabilization(input_path, output_path)
            
        except Exception as e:
            logger.error(f"视频稳定化失败: {e}")
            return False
    
    def _calculate_transforms(self, video_path: str) -> bool:
        """计算帧间变换"""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            return False
        
        # 读取第一帧
        ret, prev_frame = cap.read()
        if not ret:
            return False
        
        prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
        
        self.transforms = []
        
        while True:
            ret, curr_frame = cap.read()
            if not ret:
                break
            
            curr_gray = cv2.cvtColor(curr_frame, cv2.COLOR_BGR2GRAY)
            
            # 检测特征点
            prev_pts = cv2.goodFeaturesToTrack(
                prev_gray, maxCorners=200, qualityLevel=0.01, 
                minDistance=30, blockSize=3
            )
            
            if prev_pts is not None:
                # 光流跟踪
                curr_pts, status, _ = cv2.calcOpticalFlowPyrLK(
                    prev_gray, curr_gray, prev_pts, None
                )
                
                # 过滤有效点
                valid_prev = prev_pts[status == 1]
                valid_curr = curr_pts[status == 1]
                
                if len(valid_prev) >= 4:
                    # 计算变换矩阵
                    transform = cv2.estimateAffinePartial2D(
                        valid_prev, valid_curr
                    )[0]
                    
                    if transform is not None:
                        dx = transform[0, 2]
                        dy = transform[1, 2]
                        da = np.arctan2(transform[1, 0], transform[0, 0])
                        
                        self.transforms.append([dx, dy, da])
                    else:
                        self.transforms.append([0, 0, 0])
                else:
                    self.transforms.append([0, 0, 0])
            else:
                self.transforms.append([0, 0, 0])
            
            prev_gray = curr_gray
        
        cap.release()
        return len(self.transforms) > 0
    
    def _smooth_trajectory(self) -> None:
        """平滑轨迹"""
        # 计算轨迹
        self.trajectory = []
        x, y, a = 0, 0, 0
        
        for transform in self.transforms:
            x += transform[0]
            y += transform[1]
            a += transform[2]
            self.trajectory.append([x, y, a])
        
        # 平滑轨迹
        self.smoothed_trajectory = []
        
        for i in range(len(self.trajectory)):
            sum_x, sum_y, sum_a = 0, 0, 0
            count = 0
            
            for j in range(max(0, i - self.smoothing_radius), 
                          min(len(self.trajectory), i + self.smoothing_radius + 1)):
                sum_x += self.trajectory[j][0]
                sum_y += self.trajectory[j][1]
                sum_a += self.trajectory[j][2]
                count += 1
            
            self.smoothed_trajectory.append([
                sum_x / count,
                sum_y / count,
                sum_a / count
            ])
    
    def _apply_stabilization(self, input_path: str, output_path: str) -> bool:
        """应用稳定变换"""
        cap = cv2.VideoCapture(input_path)
        
        if not cap.isOpened():
            return False
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        frame_index = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            if frame_index < len(self.transforms):
                # 计算新的变换
                transform = self.transforms[frame_index]
                trajectory = self.trajectory[frame_index]
                smoothed = self.smoothed_trajectory[frame_index]
                
                diff_x = smoothed[0] - trajectory[0]
                diff_y = smoothed[1] - trajectory[1]
                diff_a = smoothed[2] - trajectory[2]
                
                dx = transform[0] + diff_x
                dy = transform[1] + diff_y
                da = transform[2] + diff_a
                
                # 构建变换矩阵
                T = np.array([
                    [np.cos(da), -np.sin(da), dx],
                    [np.sin(da), np.cos(da), dy]
                ], dtype=np.float32)
                
                # 应用变换
                stabilized_frame = cv2.warpAffine(frame, T, (width, height))
                out.write(stabilized_frame)
            else:
                out.write(frame)
            
            frame_index += 1
        
        cap.release()
        out.release()
        return True
