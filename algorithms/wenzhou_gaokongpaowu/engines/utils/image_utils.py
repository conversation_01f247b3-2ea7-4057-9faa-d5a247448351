"""
图像处理工具模块
提供图像处理相关的工具函数
"""

import cv2
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from PIL import Image, ImageDraw, ImageFont
import base64
import io


class ImageUtils:
    """图像处理工具类"""
    
    @staticmethod
    def draw_detections(
        image: np.ndarray, 
        detections: List[Dict[str, Any]], 
        colors: Optional[List[Tuple[int, int, int]]] = None
    ) -> np.ndarray:
        """
        在图像上绘制检测结果
        
        Args:
            image: 输入图像
            detections: 检测结果列表
            colors: 颜色列表
            
        Returns:
            绘制了检测结果的图像
        """
        if colors is None:
            colors = [
                (0, 255, 0),    # 绿色
                (255, 0, 0),    # 蓝色
                (0, 0, 255),    # 红色
                (255, 255, 0),  # 青色
                (255, 0, 255),  # 品红色
                (0, 255, 255),  # 黄色
            ]
        
        result_image = image.copy()
        
        for i, detection in enumerate(detections):
            bbox = detection.get('bbox', [])
            if len(bbox) != 4:
                continue
            
            x, y, w, h = bbox
            track_id = detection.get('track_id', 'unknown')
            confidence = detection.get('confidence', 0.0)
            velocity = detection.get('velocity', 0.0)
            
            # 选择颜色
            color = colors[i % len(colors)]
            
            # 绘制边界框
            cv2.rectangle(result_image, (x, y), (x + w, y + h), color, 2)
            
            # 绘制标签
            label = f"ID:{track_id} Conf:{confidence:.2f}"
            if velocity > 0:
                label += f" Vel:{velocity:.1f}"
            
            # 计算文本大小
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            thickness = 2
            (text_width, text_height), baseline = cv2.getTextSize(
                label, font, font_scale, thickness
            )
            
            # 绘制文本背景
            cv2.rectangle(
                result_image,
                (x, y - text_height - baseline - 5),
                (x + text_width, y),
                color,
                -1
            )
            
            # 绘制文本
            cv2.putText(
                result_image,
                label,
                (x, y - baseline - 2),
                font,
                font_scale,
                (255, 255, 255),
                thickness
            )
            
            # 绘制中心点
            center_x = x + w // 2
            center_y = y + h // 2
            cv2.circle(result_image, (center_x, center_y), 3, color, -1)
        
        return result_image
    
    @staticmethod
    def draw_trajectory(
        image: np.ndarray, 
        trajectory: List[Tuple[int, int]], 
        color: Tuple[int, int, int] = (0, 255, 255)
    ) -> np.ndarray:
        """
        在图像上绘制轨迹
        
        Args:
            image: 输入图像
            trajectory: 轨迹点列表
            color: 轨迹颜色
            
        Returns:
            绘制了轨迹的图像
        """
        result_image = image.copy()
        
        if len(trajectory) < 2:
            return result_image
        
        # 绘制轨迹线
        for i in range(1, len(trajectory)):
            cv2.line(result_image, trajectory[i-1], trajectory[i], color, 2)
        
        # 绘制轨迹点
        for point in trajectory:
            cv2.circle(result_image, point, 2, color, -1)
        
        return result_image
    
    @staticmethod
    def draw_roi(
        image: np.ndarray, 
        roi_points: List[Tuple[int, int]], 
        color: Tuple[int, int, int] = (255, 255, 0)
    ) -> np.ndarray:
        """
        在图像上绘制ROI区域
        
        Args:
            image: 输入图像
            roi_points: ROI区域点列表
            color: ROI颜色
            
        Returns:
            绘制了ROI的图像
        """
        result_image = image.copy()
        
        if len(roi_points) < 3:
            return result_image
        
        # 转换为numpy数组
        points = np.array(roi_points, np.int32)
        points = points.reshape((-1, 1, 2))
        
        # 绘制多边形
        cv2.polylines(result_image, [points], True, color, 2)
        
        # 绘制半透明填充
        overlay = result_image.copy()
        cv2.fillPoly(overlay, [points], color)
        cv2.addWeighted(result_image, 0.8, overlay, 0.2, 0, result_image)
        
        return result_image
    
    @staticmethod
    def resize_image(
        image: np.ndarray, 
        target_size: Tuple[int, int], 
        keep_aspect_ratio: bool = True
    ) -> np.ndarray:
        """
        调整图像大小
        
        Args:
            image: 输入图像
            target_size: 目标尺寸 (width, height)
            keep_aspect_ratio: 是否保持宽高比
            
        Returns:
            调整大小后的图像
        """
        if not keep_aspect_ratio:
            return cv2.resize(image, target_size)
        
        h, w = image.shape[:2]
        target_w, target_h = target_size
        
        # 计算缩放比例
        scale = min(target_w / w, target_h / h)
        
        # 计算新的尺寸
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        # 调整大小
        resized = cv2.resize(image, (new_w, new_h))
        
        # 创建目标尺寸的画布
        result = np.zeros((target_h, target_w, 3), dtype=np.uint8)
        
        # 计算居中位置
        y_offset = (target_h - new_h) // 2
        x_offset = (target_w - new_w) // 2
        
        # 将调整后的图像放置在画布中心
        result[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
        
        return result
    
    @staticmethod
    def enhance_image(image: np.ndarray) -> np.ndarray:
        """
        增强图像质量
        
        Args:
            image: 输入图像
            
        Returns:
            增强后的图像
        """
        # 转换为LAB颜色空间
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # 应用CLAHE到L通道
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        # 合并通道
        enhanced_lab = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
        
        return enhanced
    
    @staticmethod
    def image_to_base64(image: np.ndarray, format: str = 'JPEG') -> str:
        """
        将图像转换为base64字符串
        
        Args:
            image: 输入图像
            format: 图像格式
            
        Returns:
            base64编码的图像字符串
        """
        # 转换BGR到RGB
        if len(image.shape) == 3 and image.shape[2] == 3:
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:
            image_rgb = image
        
        # 转换为PIL图像
        pil_image = Image.fromarray(image_rgb)
        
        # 保存到内存缓冲区
        buffer = io.BytesIO()
        pil_image.save(buffer, format=format)
        
        # 编码为base64
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return image_base64
    
    @staticmethod
    def base64_to_image(base64_string: str) -> np.ndarray:
        """
        将base64字符串转换为图像
        
        Args:
            base64_string: base64编码的图像字符串
            
        Returns:
            图像数组
        """
        # 解码base64
        image_data = base64.b64decode(base64_string)
        
        # 转换为PIL图像
        pil_image = Image.open(io.BytesIO(image_data))
        
        # 转换为numpy数组
        image_array = np.array(pil_image)
        
        # 转换RGB到BGR
        if len(image_array.shape) == 3 and image_array.shape[2] == 3:
            image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
        else:
            image_bgr = image_array
        
        return image_bgr
    
    @staticmethod
    def create_thumbnail(
        image: np.ndarray, 
        max_size: int = 200
    ) -> np.ndarray:
        """
        创建图像缩略图
        
        Args:
            image: 输入图像
            max_size: 最大尺寸
            
        Returns:
            缩略图
        """
        h, w = image.shape[:2]
        
        if max(h, w) <= max_size:
            return image
        
        if h > w:
            new_h = max_size
            new_w = int(w * max_size / h)
        else:
            new_w = max_size
            new_h = int(h * max_size / w)
        
        thumbnail = cv2.resize(image, (new_w, new_h))
        return thumbnail
