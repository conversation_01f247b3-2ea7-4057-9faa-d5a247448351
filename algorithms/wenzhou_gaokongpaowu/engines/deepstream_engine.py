"""
DeepStream引擎适配器
将DeepStream检测模块适配为统一的引擎接口
"""

import asyncio
import json
import os
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger


class DeepStreamManager:
    """DeepStream管理器"""
    
    def __init__(self):
        self.process: Optional[subprocess.Popen] = None
        self.config_file = None
        self.temp_dir = None
        self.deepstream_executable = None
        self.is_ready = False
        
    async def initialize(self) -> bool:
        """初始化DeepStream管理器"""
        try:
            logger.info("正在初始化DeepStream管理器...")
            
            # 查找DeepStream可执行文件
            self.deepstream_executable = self._find_deepstream_executable()
            if not self.deepstream_executable:
                logger.error("未找到DeepStream可执行文件")
                return False
            
            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp(prefix="deepstream_")
            logger.info(f"创建临时目录: {self.temp_dir}")
            
            # 创建配置文件
            self.config_file = self._create_config_file()
            
            self.is_ready = True
            logger.info("DeepStream管理器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"DeepStream管理器初始化失败: {e}")
            return False
    
    def _find_deepstream_executable(self) -> Optional[str]:
        """查找DeepStream可执行文件"""
        # 可能的路径
        possible_paths = [
            "/app/deepstream-app-ext",  # Docker容器中的路径
            "./deepstream-app-ext",     # 当前目录
            "/opt/nvidia/deepstream/deepstream/bin/deepstream-app",  # 标准安装路径
            "deepstream-app"            # PATH中的命令
        ]
        
        for path in possible_paths:
            if Path(path).exists() and os.access(path, os.X_OK):
                logger.info(f"找到DeepStream可执行文件: {path}")
                return path
        
        # 尝试在PATH中查找
        try:
            result = subprocess.run(
                ["which", "deepstream-app-ext"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                path = result.stdout.strip()
                logger.info(f"在PATH中找到DeepStream可执行文件: {path}")
                return path
        except:
            pass
        
        return None
    
    def _create_config_file(self) -> str:
        """创建DeepStream配置文件"""
        config_content = """[application]
enable-perf-measurement=1
perf-measurement-interval-sec=5

[sink0]
enable=1
type=1
sync=0
source-id=0
gpu-id=0
nvbuf-memory-type=0

[streammux]
gpu-id=0
live-source=0
batch-size=1
batched-push-timeout=40000
width=1920
height=1080
enable-padding=0
nvbuf-memory-type=0
attach-sys-ts-as-ntp=1

[custom-probe]
enable=1
debug=0
custom-func=SACCustomProbeMessageSender
custom-lib=../libs/libsac_custom_probe_message_sender.so
detect-duration=2
detect-sensitivity=0.8
warning-interval=30
warning-times=1
record=0
task-id=142
model-name=High-altitude-throwing

[tracker]
enable=1
tracker-width=640
tracker-height=384
ll-config-file=config_tracker_NvDCF_perf.yml
ll-lib-file=/opt/nvidia/deepstream/deepstream/lib/libnvds_nvmultiobjecttracker.so
enable-batch-process=1
enable-past-frame=0
display-tracking-id=1

[source0]
camera-id=default
uri=file:///tmp/input.jpg
enable=1
type=2
gpu-id=0
cudadec-memtype=0
num-sources=1
"""
        
        config_path = os.path.join(self.temp_dir, "deepstream_config.txt")
        with open(config_path, 'w') as f:
            f.write(config_content)
        
        logger.info(f"创建配置文件: {config_path}")
        return config_path
    
    async def process_image(self, image_data: bytes, **kwargs) -> Dict[str, Any]:
        """处理图像"""
        if not self.is_ready:
            raise RuntimeError("DeepStream管理器未初始化")
        
        try:
            # 保存图像到临时文件
            input_path = os.path.join(self.temp_dir, "input.jpg")
            with open(input_path, 'wb') as f:
                f.write(image_data)
            
            # 更新配置文件
            self._update_config(kwargs)
            
            # 运行DeepStream
            result = await self._run_deepstream()
            
            return result
            
        except Exception as e:
            logger.error(f"DeepStream图像处理失败: {e}")
            raise
    
    def _update_config(self, params: Dict[str, Any]):
        """更新配置文件参数"""
        camera_id = params.get('camera_id', 'default')
        task_id = params.get('task_id', '142')
        sensitivity = params.get('sensitivity', 0.8)
        
        # 读取现有配置
        with open(self.config_file, 'r') as f:
            content = f.read()
        
        # 更新参数
        content = content.replace('camera-id=default', f'camera-id={camera_id}')
        content = content.replace('task-id=142', f'task-id={task_id}')
        content = content.replace('detect-sensitivity=0.8', f'detect-sensitivity={sensitivity}')
        
        # 写回配置文件
        with open(self.config_file, 'w') as f:
            f.write(content)
    
    async def _run_deepstream(self) -> Dict[str, Any]:
        """运行DeepStream处理"""
        try:
            # 构建命令
            cmd = [
                self.deepstream_executable,
                "-c", self.config_file
            ]
            
            logger.debug(f"执行DeepStream命令: {' '.join(cmd)}")
            
            # 运行DeepStream
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.temp_dir,
                env=dict(os.environ, **{
                    "DISPLAY": ":0",
                    "GST_DEBUG": "1"
                })
            )
            
            # 等待处理完成
            stdout, stderr = process.communicate(timeout=30)
            
            if process.returncode == 0:
                # 解析输出
                result = self._parse_deepstream_output(stdout.decode())
                return result
            else:
                logger.error(f"DeepStream执行失败: {stderr.decode()}")
                return self._create_default_result()
                
        except subprocess.TimeoutExpired:
            logger.warning("DeepStream执行超时")
            if process:
                process.kill()
            return self._create_default_result()
        except Exception as e:
            logger.error(f"DeepStream执行异常: {e}")
            return self._create_default_result()
    
    def _parse_deepstream_output(self, output: str) -> Dict[str, Any]:
        """解析DeepStream输出"""
        # 查找JSON输出
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('{') and 'High-altitude-throwing' in line:
                try:
                    result = json.loads(line)
                    logger.info("成功解析DeepStream输出")
                    return result
                except json.JSONDecodeError:
                    continue
        
        logger.warning("未找到有效的DeepStream JSON输出")
        return self._create_default_result()
    
    def _create_default_result(self) -> Dict[str, Any]:
        """创建默认结果"""
        return {
            "version": "1.0",
            "task": {
                "model_name": "High-altitude-throwing",
                "task_id": "142"
            },
            "events": {
                "camera_id": "default",
                "timestamp": int(time.time() * 1000),
                "image": {
                    "path": "processed.jpg",
                    "uri": "file://input.jpg",
                    "width": 1920,
                    "height": 1080
                },
                "objects": []
            }
        }
    
    def is_running(self) -> bool:
        """检查DeepStream是否在运行"""
        return self.process is not None and self.process.poll() is None
    
    async def cleanup(self):
        """清理资源"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.process.kill()
            except:
                pass
            finally:
                self.process = None
        
        # 清理临时目录
        if self.temp_dir and Path(self.temp_dir).exists():
            try:
                import shutil
                shutil.rmtree(self.temp_dir)
                logger.info(f"清理临时目录: {self.temp_dir}")
            except:
                pass
        
        self.is_ready = False
        logger.info("DeepStream管理器资源已清理")
