"""
目标跟踪器模块
实现多目标跟踪算法，用于跟踪高空抛物对象
"""

import time
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from loguru import logger
from scipy.optimize import linear_sum_assignment

from .config import DetectionConfig


class TrackedObject:
    """被跟踪的对象"""
    
    def __init__(self, detection: Dict[str, Any], track_id: int):
        self.track_id = track_id
        self.bbox = detection['bbox']
        self.confidence = detection['confidence']
        self.velocity = detection.get('velocity', 0.0)
        self.acceleration = detection.get('acceleration', 0.0)
        
        # 跟踪状态
        self.hits = 1
        self.hit_streak = 1
        self.age = 1
        self.time_since_update = 0
        self.last_update_time = time.time()
        
        # 历史信息
        self.history = [detection]
        self.center_history = [self._get_center(detection['bbox'])]
        
        # 预测状态
        self.predicted_bbox = detection['bbox'].copy()
        
    def update(self, detection: Dict[str, Any]) -> None:
        """更新跟踪对象"""
        self.time_since_update = 0
        self.history.append(detection)
        self.hits += 1
        self.hit_streak += 1
        self.last_update_time = time.time()
        
        # 更新位置和属性
        self.bbox = detection['bbox']
        self.confidence = detection['confidence']
        self.velocity = detection.get('velocity', self.velocity)
        self.acceleration = detection.get('acceleration', self.acceleration)
        
        # 更新中心点历史
        center = self._get_center(detection['bbox'])
        self.center_history.append(center)
        
        # 保持历史长度
        max_history = 10
        if len(self.history) > max_history:
            self.history.pop(0)
            self.center_history.pop(0)
    
    def predict(self) -> None:
        """预测下一帧的位置"""
        self.age += 1
        if self.time_since_update > 0:
            self.hit_streak = 0
        self.time_since_update += 1
        
        # 简单的线性预测
        if len(self.center_history) >= 2:
            # 计算速度向量
            current_center = self.center_history[-1]
            prev_center = self.center_history[-2]
            
            dx = current_center[0] - prev_center[0]
            dy = current_center[1] - prev_center[1]
            
            # 预测新的中心点
            predicted_center = [
                current_center[0] + dx,
                current_center[1] + dy
            ]
            
            # 更新预测边界框
            bbox_width = self.bbox[2]
            bbox_height = self.bbox[3]
            
            self.predicted_bbox = [
                int(predicted_center[0] - bbox_width // 2),
                int(predicted_center[1] - bbox_height // 2),
                bbox_width,
                bbox_height
            ]
    
    def get_state(self) -> Dict[str, Any]:
        """获取当前状态"""
        return {
            'track_id': self.track_id,
            'bbox': self.bbox,
            'confidence': self.confidence,
            'velocity': self.velocity,
            'acceleration': self.acceleration,
            'hits': self.hits,
            'age': self.age,
            'time_since_update': self.time_since_update
        }
    
    def _get_center(self, bbox: List[int]) -> List[int]:
        """获取边界框中心点"""
        return [
            bbox[0] + bbox[2] // 2,
            bbox[1] + bbox[3] // 2
        ]


class ObjectTracker:
    """多目标跟踪器"""
    
    def __init__(self, config: DetectionConfig):
        self.config = config
        self.tracks: List[TrackedObject] = []
        self.next_track_id = 1
        self.frame_count = 0
        
        # 跟踪参数
        self.max_disappeared = config.max_disappeared
        self.max_distance = config.max_distance
        self.min_hits = config.min_hits
        
    async def update(self, detections: List[Dict[str, Any]], camera_id: str = "default") -> List[Dict[str, Any]]:
        """
        更新跟踪器
        
        Args:
            detections: 检测结果列表
            camera_id: 摄像头ID
            
        Returns:
            跟踪结果列表
        """
        self.frame_count += 1
        
        # 预测所有现有轨迹的位置
        for track in self.tracks:
            track.predict()
        
        # 数据关联
        matched_tracks, unmatched_detections, unmatched_tracks = self._associate_detections_to_tracks(
            detections, self.tracks
        )
        
        # 更新匹配的轨迹
        for track_idx, detection_idx in matched_tracks:
            self.tracks[track_idx].update(detections[detection_idx])
        
        # 为未匹配的检测创建新轨迹
        for detection_idx in unmatched_detections:
            new_track = TrackedObject(detections[detection_idx], self.next_track_id)
            self.tracks.append(new_track)
            self.next_track_id += 1
        
        # 删除长时间未更新的轨迹
        self.tracks = [
            track for track in self.tracks
            if track.time_since_update <= self.max_disappeared
        ]
        
        # 返回有效的跟踪结果
        valid_tracks = []
        for track in self.tracks:
            # 只返回满足最小命中次数的轨迹
            if track.hits >= self.min_hits or track.hit_streak >= 1:
                valid_tracks.append(track.get_state())
        
        logger.debug(f"跟踪更新完成: {len(valid_tracks)} 个有效轨迹")
        return valid_tracks
    
    def _associate_detections_to_tracks(
        self, 
        detections: List[Dict[str, Any]], 
        tracks: List[TrackedObject]
    ) -> Tuple[List[Tuple[int, int]], List[int], List[int]]:
        """
        将检测结果与现有轨迹进行关联
        
        Returns:
            (匹配的轨迹对, 未匹配的检测, 未匹配的轨迹)
        """
        if len(tracks) == 0:
            return [], list(range(len(detections))), []
        
        if len(detections) == 0:
            return [], [], list(range(len(tracks)))
        
        # 计算IoU矩阵
        iou_matrix = self._compute_iou_matrix(detections, tracks)
        
        # 使用匈牙利算法进行最优匹配
        if iou_matrix.size > 0:
            # 将IoU转换为成本矩阵（1 - IoU）
            cost_matrix = 1 - iou_matrix
            
            # 应用匈牙利算法
            track_indices, detection_indices = linear_sum_assignment(cost_matrix)
            
            # 过滤低质量匹配
            matched_tracks = []
            for track_idx, detection_idx in zip(track_indices, detection_indices):
                if iou_matrix[track_idx, detection_idx] > 0.1:  # IoU阈值
                    matched_tracks.append((track_idx, detection_idx))
            
            # 找出未匹配的检测和轨迹
            matched_detection_indices = [detection_idx for _, detection_idx in matched_tracks]
            matched_track_indices = [track_idx for track_idx, _ in matched_tracks]
            
            unmatched_detections = [
                i for i in range(len(detections)) 
                if i not in matched_detection_indices
            ]
            unmatched_tracks = [
                i for i in range(len(tracks)) 
                if i not in matched_track_indices
            ]
            
            return matched_tracks, unmatched_detections, unmatched_tracks
        
        return [], list(range(len(detections))), list(range(len(tracks)))
    
    def _compute_iou_matrix(
        self, 
        detections: List[Dict[str, Any]], 
        tracks: List[TrackedObject]
    ) -> np.ndarray:
        """计算IoU矩阵"""
        iou_matrix = np.zeros((len(tracks), len(detections)))
        
        for track_idx, track in enumerate(tracks):
            for detection_idx, detection in enumerate(detections):
                iou = self._compute_iou(track.predicted_bbox, detection['bbox'])
                iou_matrix[track_idx, detection_idx] = iou
        
        return iou_matrix
    
    def _compute_iou(self, bbox1: List[int], bbox2: List[int]) -> float:
        """
        计算两个边界框的IoU
        
        Args:
            bbox1, bbox2: [x, y, width, height] 格式的边界框
            
        Returns:
            IoU值
        """
        # 转换为 [x1, y1, x2, y2] 格式
        box1 = [bbox1[0], bbox1[1], bbox1[0] + bbox1[2], bbox1[1] + bbox1[3]]
        box2 = [bbox2[0], bbox2[1], bbox2[0] + bbox2[2], bbox2[1] + bbox2[3]]
        
        # 计算交集
        x1 = max(box1[0], box2[0])
        y1 = max(box1[1], box2[1])
        x2 = min(box1[2], box2[2])
        y2 = min(box1[3], box2[3])
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        intersection = (x2 - x1) * (y2 - y1)
        
        # 计算并集
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection
        
        if union <= 0:
            return 0.0
        
        return intersection / union
    
    def get_track_count(self) -> int:
        """获取当前活跃轨迹数量"""
        return len(self.tracks)
    
    def get_track_by_id(self, track_id: int) -> Optional[TrackedObject]:
        """根据ID获取轨迹"""
        for track in self.tracks:
            if track.track_id == track_id:
                return track
        return None
    
    def clear_tracks(self) -> None:
        """清除所有轨迹"""
        self.tracks.clear()
        self.next_track_id = 1
        self.frame_count = 0
        logger.info("所有轨迹已清除")
    
    async def cleanup(self) -> None:
        """清理资源"""
        logger.info("正在清理跟踪器资源...")
        self.clear_tracks()
        logger.info("跟踪器资源清理完成")
