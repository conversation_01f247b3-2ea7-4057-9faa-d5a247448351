"""
高空抛物检测器模块
实现基于计算机视觉的高空抛物行为检测算法
"""

import asyncio
import time
from typing import List, Dict, Tuple, Optional, Any
import numpy as np
import cv2
from loguru import logger

from .config import DetectionConfig


class HighAltitudeThrowingDetector:
    """高空抛物检测器"""
    
    def __init__(self, config: DetectionConfig):
        self.config = config
        self.is_initialized = False
        self.model = None
        self.background_subtractor = None
        self.motion_history = {}
        self.detection_cache = {}
        
        # 运动检测参数
        self.motion_threshold = 25
        self.min_contour_area = 500
        self.max_contour_area = 50000
        
        # 轨迹分析参数
        self.trajectory_buffer = {}
        self.velocity_buffer = {}
        
    async def initialize(self) -> None:
        """初始化检测器"""
        try:
            logger.info("正在初始化高空抛物检测器...")
            
            # 初始化背景减除器
            self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
                detectShadows=True,
                varThreshold=16,
                history=500
            )
            
            # 初始化形态学操作核
            self.morph_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            
            # 初始化光流检测器
            self.lk_params = dict(
                winSize=(15, 15),
                maxLevel=2,
                criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.03)
            )
            
            self.is_initialized = True
            logger.info("高空抛物检测器初始化完成")
            
        except Exception as e:
            logger.error(f"检测器初始化失败: {e}")
            raise
    
    def is_ready(self) -> bool:
        """检查检测器是否就绪"""
        return self.is_initialized
    
    async def detect(self, image: np.ndarray, sensitivity: float = None) -> List[Dict[str, Any]]:
        """
        检测图像中的高空抛物行为
        
        Args:
            image: 输入图像
            sensitivity: 检测灵敏度
            
        Returns:
            检测结果列表
        """
        if not self.is_initialized:
            raise RuntimeError("检测器未初始化")
        
        if sensitivity is None:
            sensitivity = self.config.detect_sensitivity
        
        try:
            # 预处理图像
            processed_image = self._preprocess_image(image)
            
            # 运动检测
            motion_objects = self._detect_motion(processed_image)
            
            # 轨迹分析
            trajectory_objects = self._analyze_trajectories(motion_objects, image.shape)
            
            # 高空抛物行为识别
            throwing_objects = self._identify_throwing_behavior(
                trajectory_objects, sensitivity
            )
            
            # 后处理
            final_detections = self._postprocess_detections(throwing_objects, image.shape)
            
            return final_detections
            
        except Exception as e:
            logger.error(f"检测过程中发生错误: {e}")
            return []
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """预处理图像"""
        # 调整图像大小
        if image.shape[:2] != (self.config.resize_height, self.config.resize_width):
            image = cv2.resize(
                image, 
                (self.config.resize_width, self.config.resize_height)
            )
        
        # 高斯模糊减噪
        image = cv2.GaussianBlur(image, (5, 5), 0)
        
        return image
    
    def _detect_motion(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """检测运动对象"""
        # 背景减除
        fg_mask = self.background_subtractor.apply(image)
        
        # 形态学操作
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, self.morph_kernel)
        fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, self.morph_kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(
            fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )
        
        motion_objects = []
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # 过滤小面积和大面积的轮廓
            if self.min_contour_area < area < self.max_contour_area:
                # 计算边界框
                x, y, w, h = cv2.boundingRect(contour)
                
                # 计算中心点
                center_x = x + w // 2
                center_y = y + h // 2
                
                motion_objects.append({
                    'bbox': [x, y, w, h],
                    'center': [center_x, center_y],
                    'area': area,
                    'contour': contour,
                    'timestamp': time.time()
                })
        
        return motion_objects
    
    def _analyze_trajectories(self, motion_objects: List[Dict], image_shape: Tuple[int, int]) -> List[Dict]:
        """分析运动轨迹"""
        current_time = time.time()
        analyzed_objects = []
        
        for obj in motion_objects:
            center = tuple(obj['center'])
            obj_id = self._get_object_id(center, current_time)
            
            # 更新轨迹缓存
            if obj_id not in self.trajectory_buffer:
                self.trajectory_buffer[obj_id] = []
                self.velocity_buffer[obj_id] = []
            
            self.trajectory_buffer[obj_id].append({
                'center': center,
                'timestamp': current_time,
                'bbox': obj['bbox']
            })
            
            # 保持轨迹长度
            if len(self.trajectory_buffer[obj_id]) > self.config.trajectory_length:
                self.trajectory_buffer[obj_id].pop(0)
            
            # 计算速度和加速度
            velocity, acceleration = self._calculate_motion_parameters(obj_id)
            
            obj.update({
                'object_id': obj_id,
                'velocity': velocity,
                'acceleration': acceleration,
                'trajectory': self.trajectory_buffer[obj_id].copy()
            })
            
            analyzed_objects.append(obj)
        
        return analyzed_objects
    
    def _get_object_id(self, center: Tuple[int, int], timestamp: float) -> str:
        """获取对象ID（简单的基于距离的关联）"""
        min_distance = float('inf')
        best_id = None
        
        # 查找最近的已知对象
        for obj_id, trajectory in self.trajectory_buffer.items():
            if trajectory:
                last_point = trajectory[-1]
                if timestamp - last_point['timestamp'] < 1.0:  # 1秒内
                    distance = np.sqrt(
                        (center[0] - last_point['center'][0]) ** 2 +
                        (center[1] - last_point['center'][1]) ** 2
                    )
                    if distance < min_distance and distance < self.config.max_distance:
                        min_distance = distance
                        best_id = obj_id
        
        # 如果没有找到匹配的对象，创建新ID
        if best_id is None:
            best_id = f"obj_{int(timestamp * 1000) % 100000}"
        
        return best_id
    
    def _calculate_motion_parameters(self, obj_id: str) -> Tuple[float, float]:
        """计算运动参数（速度和加速度）"""
        trajectory = self.trajectory_buffer[obj_id]
        
        if len(trajectory) < 2:
            return 0.0, 0.0
        
        # 计算速度
        velocities = []
        for i in range(1, len(trajectory)):
            dt = trajectory[i]['timestamp'] - trajectory[i-1]['timestamp']
            if dt > 0:
                dx = trajectory[i]['center'][0] - trajectory[i-1]['center'][0]
                dy = trajectory[i]['center'][1] - trajectory[i-1]['center'][1]
                velocity = np.sqrt(dx**2 + dy**2) / dt
                velocities.append(velocity)
        
        avg_velocity = np.mean(velocities) if velocities else 0.0
        
        # 计算加速度
        acceleration = 0.0
        if len(velocities) >= 2:
            velocity_changes = np.diff(velocities)
            time_intervals = [
                trajectory[i+1]['timestamp'] - trajectory[i]['timestamp']
                for i in range(1, len(trajectory)-1)
            ]
            if time_intervals:
                accelerations = [
                    dv / dt for dv, dt in zip(velocity_changes, time_intervals) if dt > 0
                ]
                acceleration = np.mean(accelerations) if accelerations else 0.0
        
        return avg_velocity, acceleration
    
    def _identify_throwing_behavior(self, objects: List[Dict], sensitivity: float) -> List[Dict]:
        """识别高空抛物行为"""
        throwing_objects = []
        
        for obj in objects:
            is_throwing = self._is_throwing_behavior(obj, sensitivity)
            
            if is_throwing:
                obj['confidence'] = self._calculate_confidence(obj, sensitivity)
                obj['behavior_type'] = 'high-altitude-throwing'
                throwing_objects.append(obj)
        
        return throwing_objects
    
    def _is_throwing_behavior(self, obj: Dict, sensitivity: float) -> bool:
        """判断是否为抛物行为"""
        velocity = obj.get('velocity', 0)
        acceleration = obj.get('acceleration', 0)
        trajectory = obj.get('trajectory', [])
        
        # 基本条件检查
        if len(trajectory) < 3:
            return False
        
        # 速度阈值检查（调整灵敏度）
        velocity_threshold = self.config.velocity_threshold * sensitivity
        if velocity < velocity_threshold:
            return False
        
        # 轨迹形状分析（抛物线特征）
        if self._analyze_parabolic_trajectory(trajectory):
            return True
        
        # 加速度模式分析
        if abs(acceleration) > self.config.acceleration_threshold * sensitivity:
            return True
        
        return False
    
    def _analyze_parabolic_trajectory(self, trajectory: List[Dict]) -> bool:
        """分析轨迹是否具有抛物线特征"""
        if len(trajectory) < 5:
            return False
        
        # 提取Y坐标序列
        y_coords = [point['center'][1] for point in trajectory]
        
        # 检查是否有明显的向下运动趋势
        if len(y_coords) >= 3:
            # 计算二阶差分来检测抛物线特征
            first_diff = np.diff(y_coords)
            second_diff = np.diff(first_diff)
            
            # 抛物线的二阶差分应该相对稳定且为正（向下加速）
            if len(second_diff) > 0 and np.mean(second_diff) > 0:
                return True
        
        return False
    
    def _calculate_confidence(self, obj: Dict, sensitivity: float) -> float:
        """计算检测置信度"""
        base_confidence = 0.5
        
        # 基于速度的置信度
        velocity = obj.get('velocity', 0)
        velocity_confidence = min(velocity / self.config.velocity_threshold, 1.0)
        
        # 基于轨迹长度的置信度
        trajectory_length = len(obj.get('trajectory', []))
        trajectory_confidence = min(trajectory_length / self.config.trajectory_length, 1.0)
        
        # 基于面积的置信度
        area = obj.get('area', 0)
        area_confidence = min(area / self.max_contour_area, 1.0)
        
        # 综合置信度
        confidence = (
            base_confidence * 0.3 +
            velocity_confidence * 0.4 +
            trajectory_confidence * 0.2 +
            area_confidence * 0.1
        ) * sensitivity
        
        return min(confidence, 1.0)
    
    def _postprocess_detections(self, detections: List[Dict], original_shape: Tuple[int, int]) -> List[Dict]:
        """后处理检测结果"""
        processed_detections = []
        
        for detection in detections:
            # 将坐标从调整后的尺寸映射回原始尺寸
            bbox = detection['bbox']
            scale_x = original_shape[1] / self.config.resize_width
            scale_y = original_shape[0] / self.config.resize_height
            
            scaled_bbox = [
                int(bbox[0] * scale_x),
                int(bbox[1] * scale_y),
                int(bbox[2] * scale_x),
                int(bbox[3] * scale_y)
            ]
            
            processed_detection = {
                'bbox': scaled_bbox,
                'confidence': detection.get('confidence', 0.5),
                'track_id': detection.get('object_id', 'unknown'),
                'velocity': detection.get('velocity', 0),
                'acceleration': detection.get('acceleration', 0),
                'behavior_type': detection.get('behavior_type', 'high-altitude-throwing')
            }
            
            processed_detections.append(processed_detection)
        
        return processed_detections
    
    async def cleanup(self) -> None:
        """清理资源"""
        logger.info("正在清理检测器资源...")
        self.trajectory_buffer.clear()
        self.velocity_buffer.clear()
        self.detection_cache.clear()
        self.is_initialized = False
        logger.info("检测器资源清理完成")
