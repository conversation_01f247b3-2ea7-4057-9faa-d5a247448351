"""
检测配置管理模块
管理高空抛物检测算法的各种配置参数
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
import json
from pathlib import Path


@dataclass
class DetectionConfig:
    """检测配置类"""
    
    # 基本配置
    model_name: str = "High-altitude-throwing"
    version: str = "1.0.0"
    
    # 检测参数
    detect_duration: float = 2.0  # 检测持续时间 (秒)
    detect_sensitivity: float = 0.8  # 检测灵敏度 (0.0-1.0)
    confidence_threshold: float = 0.5  # 置信度阈值
    nms_threshold: float = 0.4  # NMS阈值
    
    # 报警参数
    warning_interval: int = 30  # 报警间隔 (秒)
    warning_times: int = -1  # 报警次数 (-1表示无限制)
    
    # 录制参数
    record_enabled: bool = False  # 是否开启录制
    record_duration: int = 10  # 录制时长 (秒)
    record_cache: int = 25  # 录制缓存 (秒)
    record_start_time: int = 10  # 录制开始时间 (秒)
    
    # 图像处理参数
    input_width: int = 1920
    input_height: int = 1080
    resize_width: int = 640
    resize_height: int = 384
    
    # ROI区域 (归一化坐标)
    roi_coordinates: List[List[float]] = field(default_factory=lambda: [
        [0.0, 0.0, 1.0, 0.0, 1.0, 1.0, 0.0, 1.0]  # 默认全屏
    ])
    
    # 跟踪参数
    max_disappeared: int = 30  # 最大消失帧数
    max_distance: float = 100.0  # 最大跟踪距离
    min_hits: int = 3  # 最小命中次数
    
    # 运动分析参数
    velocity_threshold: float = 50.0  # 速度阈值 (像素/秒)
    acceleration_threshold: float = 100.0  # 加速度阈值 (像素/秒²)
    trajectory_length: int = 10  # 轨迹长度
    
    # 文件路径
    model_path: str = "models/high_altitude_throwing.onnx"
    config_file: str = "config/detection_config.json"
    log_dir: str = "logs"
    data_dir: str = "data"
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保目录存在
        Path(self.log_dir).mkdir(exist_ok=True)
        Path(self.data_dir).mkdir(exist_ok=True)
        Path(f"{self.data_dir}/images").mkdir(exist_ok=True)
        Path(f"{self.data_dir}/videos").mkdir(exist_ok=True)
    
    @classmethod
    def from_file(cls, config_path: str) -> "DetectionConfig":
        """从配置文件加载配置"""
        config_file = Path(config_path)
        if not config_file.exists():
            # 如果配置文件不存在，创建默认配置
            config = cls()
            config.save_to_file(config_path)
            return config
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return cls(**config_data)
    
    def save_to_file(self, config_path: str) -> None:
        """保存配置到文件"""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 转换为字典，排除不可序列化的字段
        config_dict = {}
        for key, value in self.__dict__.items():
            if not key.startswith('_'):
                config_dict[key] = value
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def update(self, **kwargs) -> None:
        """更新配置参数"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def get_roi_polygon(self, stream_id: int = 0) -> List[Tuple[float, float]]:
        """获取ROI多边形坐标"""
        if stream_id < len(self.roi_coordinates):
            coords = self.roi_coordinates[stream_id]
            # 将坐标列表转换为点对列表
            points = []
            for i in range(0, len(coords), 2):
                if i + 1 < len(coords):
                    points.append((coords[i], coords[i + 1]))
            return points
        return [(0.0, 0.0), (1.0, 0.0), (1.0, 1.0), (0.0, 1.0)]
    
    def is_point_in_roi(self, x: float, y: float, stream_id: int = 0) -> bool:
        """检查点是否在ROI区域内"""
        roi_points = self.get_roi_polygon(stream_id)
        
        # 使用射线法判断点是否在多边形内
        n = len(roi_points)
        inside = False
        
        p1x, p1y = roi_points[0]
        for i in range(1, n + 1):
            p2x, p2y = roi_points[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def validate(self) -> List[str]:
        """验证配置参数的有效性"""
        errors = []
        
        if not (0.0 <= self.detect_sensitivity <= 1.0):
            errors.append("detect_sensitivity must be between 0.0 and 1.0")
        
        if not (0.0 <= self.confidence_threshold <= 1.0):
            errors.append("confidence_threshold must be between 0.0 and 1.0")
        
        if not (0.0 <= self.nms_threshold <= 1.0):
            errors.append("nms_threshold must be between 0.0 and 1.0")
        
        if self.detect_duration <= 0:
            errors.append("detect_duration must be positive")
        
        if self.warning_interval <= 0:
            errors.append("warning_interval must be positive")
        
        if self.input_width <= 0 or self.input_height <= 0:
            errors.append("input dimensions must be positive")
        
        if self.resize_width <= 0 or self.resize_height <= 0:
            errors.append("resize dimensions must be positive")
        
        return errors
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            key: value for key, value in self.__dict__.items()
            if not key.startswith('_')
        }
