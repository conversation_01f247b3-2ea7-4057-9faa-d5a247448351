#!/bin/bash
# 智能启动脚本
# 自动检测环境并启动相应的服务模式

set -e

echo "=========================================="
echo "温州高空抛物检测算法启动"
echo "智能自适应算法包"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 环境检测函数
check_nvidia_gpu() {
    log_info "检测NVIDIA GPU..."
    if command -v nvidia-smi &> /dev/null; then
        if nvidia-smi &> /dev/null; then
            GPU_COUNT=$(nvidia-smi --query-gpu=count --format=csv,noheader,nounits | head -1)
            GPU_MEMORY=$(nvidia-smi --query-gpu=memory.total --format=csv,noheader,nounits | head -1)
            GPU_NAME=$(nvidia-smi --query-gpu=name --format=csv,noheader | head -1)
            
            log_info "检测到NVIDIA GPU:"
            log_info "  数量: $GPU_COUNT"
            log_info "  型号: $GPU_NAME"
            log_info "  内存: ${GPU_MEMORY}MB"
            
            if [ "$GPU_MEMORY" -ge 2048 ]; then
                return 0
            else
                log_warn "GPU内存不足 (需要至少2GB)"
                return 1
            fi
        else
            log_warn "nvidia-smi命令执行失败"
            return 1
        fi
    else
        log_info "未找到nvidia-smi命令"
        return 1
    fi
}

check_cuda() {
    log_info "检测CUDA环境..."
    if command -v nvcc &> /dev/null; then
        CUDA_VERSION=$(nvcc --version | grep "release" | sed 's/.*release \([0-9.]*\).*/\1/')
        log_info "检测到CUDA版本: $CUDA_VERSION"
        return 0
    elif [ -d "/usr/local/cuda" ]; then
        log_info "检测到CUDA安装目录"
        return 0
    elif [ -n "$CUDA_HOME" ] && [ -d "$CUDA_HOME" ]; then
        log_info "检测到CUDA环境变量"
        return 0
    else
        log_info "未检测到CUDA环境"
        return 1
    fi
}

check_deepstream() {
    log_info "检测DeepStream SDK..."
    if [ -d "/opt/nvidia/deepstream/deepstream" ]; then
        if [ -f "/opt/nvidia/deepstream/deepstream/VERSION" ]; then
            DS_VERSION=$(cat /opt/nvidia/deepstream/deepstream/VERSION)
            log_info "检测到DeepStream版本: $DS_VERSION"
        else
            log_info "检测到DeepStream安装"
        fi
        return 0
    else
        log_info "未检测到DeepStream SDK"
        return 1
    fi
}

check_docker_gpu() {
    log_info "检测Docker GPU支持..."
    if [ -f "/.dockerenv" ]; then
        if ls /dev/nvidia* &> /dev/null; then
            log_info "检测到Docker GPU设备"
            return 0
        else
            log_info "Docker容器中未检测到GPU设备"
            return 1
        fi
    else
        log_info "不在Docker容器中"
        return 1
    fi
}

# 环境检测
log_info "开始环境检测..."

HAS_GPU=false
HAS_CUDA=false
HAS_DEEPSTREAM=false
HAS_DOCKER_GPU=false

if check_nvidia_gpu; then
    HAS_GPU=true
fi

if check_cuda; then
    HAS_CUDA=true
fi

if check_deepstream; then
    HAS_DEEPSTREAM=true
fi

if check_docker_gpu; then
    HAS_DOCKER_GPU=true
fi

# 确定运行模式
log_info "确定运行模式..."

RECOMMENDED_MODE="python"
if [ "$HAS_GPU" = true ] && [ "$HAS_CUDA" = true ] && [ "$HAS_DEEPSTREAM" = true ]; then
    RECOMMENDED_MODE="deepstream"
    log_info "推荐模式: DeepStream (高性能)"
else
    log_info "推荐模式: Python (兼容)"
    
    if [ "$HAS_GPU" = true ]; then
        missing_components=""
        if [ "$HAS_CUDA" = false ]; then
            missing_components="$missing_components CUDA"
        fi
        if [ "$HAS_DEEPSTREAM" = false ]; then
            missing_components="$missing_components DeepStream"
        fi
        log_warn "检测到GPU但缺少组件:$missing_components"
    fi
fi

# 设置环境变量
export RECOMMENDED_MODE
export HAS_GPU
export HAS_CUDA
export HAS_DEEPSTREAM

# 显示环境信息
echo ""
echo "=========================================="
echo "环境检测结果:"
echo "  NVIDIA GPU: $([ "$HAS_GPU" = true ] && echo "✅" || echo "❌")"
echo "  CUDA:       $([ "$HAS_CUDA" = true ] && echo "✅" || echo "❌")"
echo "  DeepStream: $([ "$HAS_DEEPSTREAM" = true ] && echo "✅" || echo "❌")"
echo "  Docker GPU: $([ "$HAS_DOCKER_GPU" = true ] && echo "✅" || echo "❌")"
echo "  推荐模式:   $RECOMMENDED_MODE"
echo "=========================================="
echo ""

# 设置DeepStream环境变量 (如果可用)
if [ "$HAS_DEEPSTREAM" = true ]; then
    export DEEPSTREAM_PATH=/opt/nvidia/deepstream/deepstream
    export LD_LIBRARY_PATH=${DEEPSTREAM_PATH}/lib:${LD_LIBRARY_PATH}
    export GST_PLUGIN_PATH=${DEEPSTREAM_PATH}/lib/gst-plugins:${GST_PLUGIN_PATH}
    log_info "设置DeepStream环境变量"
fi

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p /app/logs
mkdir -p /app/data/images
mkdir -p /app/data/temp
mkdir -p /app/data/videos
mkdir -p /app/output

# 等待一下让用户看到环境信息
sleep 2

# 启动API服务器
log_info "启动温州高空抛物检测算法服务器..."
log_info "服务将在端口8005上运行"
log_info "API文档: http://localhost:8005/docs"

# 切换到应用目录
cd /app

# 启动服务
exec python3 -m uvicorn src.api_server:app --host 0.0.0.0 --port 8005
