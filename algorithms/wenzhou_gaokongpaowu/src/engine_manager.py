"""
智能引擎管理器
根据环境自动选择和管理DeepStream或Python检测引擎
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from loguru import logger

from .environment_detector import EnvironmentDetector, EnvironmentInfo


class DetectionEngine(ABC):
    """检测引擎抽象基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.is_initialized = False
        self.initialization_time = 0.0
        self.total_detections = 0
        self.total_processing_time = 0.0
    
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化引擎"""
        pass
    
    @abstractmethod
    async def detect(self, image_data: bytes, **kwargs) -> Dict[str, Any]:
        """执行检测"""
        pass
    
    @abstractmethod
    async def cleanup(self):
        """清理资源"""
        pass
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """获取引擎状态"""
        pass
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_processing_time = (
            self.total_processing_time / self.total_detections 
            if self.total_detections > 0 else 0.0
        )
        
        return {
            "total_detections": self.total_detections,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_processing_time,
            "initialization_time": self.initialization_time
        }


class DeepStreamEngine(DetectionEngine):
    """DeepStream检测引擎"""
    
    def __init__(self):
        super().__init__("DeepStream")
        self.deepstream_manager = None
        self.process = None
    
    async def initialize(self) -> bool:
        """初始化DeepStream引擎"""
        start_time = time.time()
        
        try:
            logger.info("正在初始化DeepStream引擎...")
            
            # 导入DeepStream相关模块
            from ..engines.deepstream_engine import DeepStreamManager
            
            self.deepstream_manager = DeepStreamManager()
            
            # 启动DeepStream进程
            if await self.deepstream_manager.initialize():
                self.is_initialized = True
                self.initialization_time = time.time() - start_time
                logger.info(f"DeepStream引擎初始化成功 (耗时: {self.initialization_time:.2f}秒)")
                return True
            else:
                logger.error("DeepStream引擎初始化失败")
                return False
                
        except Exception as e:
            logger.error(f"DeepStream引擎初始化异常: {e}")
            return False
    
    async def detect(self, image_data: bytes, **kwargs) -> Dict[str, Any]:
        """使用DeepStream执行检测"""
        if not self.is_initialized:
            raise RuntimeError("DeepStream引擎未初始化")
        
        start_time = time.time()
        
        try:
            result = await self.deepstream_manager.process_image(
                image_data, **kwargs
            )
            
            processing_time = time.time() - start_time
            self.total_detections += 1
            self.total_processing_time += processing_time
            
            # 添加引擎信息到结果
            result["engine_info"] = {
                "engine": "deepstream",
                "mode": "高性能模式",
                "processing_time_ms": processing_time * 1000,
                "gpu_accelerated": True
            }
            
            return result
            
        except Exception as e:
            logger.error(f"DeepStream检测失败: {e}")
            raise
    
    async def cleanup(self):
        """清理DeepStream资源"""
        if self.deepstream_manager:
            await self.deepstream_manager.cleanup()
        self.is_initialized = False
        logger.info("DeepStream引擎资源已清理")
    
    def get_status(self) -> Dict[str, Any]:
        """获取DeepStream引擎状态"""
        return {
            "engine": "deepstream",
            "initialized": self.is_initialized,
            "process_running": self.deepstream_manager.is_running() if self.deepstream_manager else False,
            "gpu_accelerated": True,
            "concurrent_streams": 15,
            "performance_level": "high"
        }


class PythonEngine(DetectionEngine):
    """Python检测引擎"""
    
    def __init__(self):
        super().__init__("Python")
        self.detector = None
        self.tracker = None
        self.config = None
    
    async def initialize(self) -> bool:
        """初始化Python引擎"""
        start_time = time.time()
        
        try:
            logger.info("正在初始化Python引擎...")
            
            # 导入Python检测模块
            from ..engines.python_engine import (
                HighAltitudeThrowingDetector, 
                ObjectTracker, 
                DetectionConfig
            )
            
            # 初始化配置
            self.config = DetectionConfig()
            
            # 初始化检测器
            self.detector = HighAltitudeThrowingDetector(self.config)
            await self.detector.initialize()
            
            # 初始化跟踪器
            self.tracker = ObjectTracker(self.config)
            
            self.is_initialized = True
            self.initialization_time = time.time() - start_time
            logger.info(f"Python引擎初始化成功 (耗时: {self.initialization_time:.2f}秒)")
            return True
            
        except Exception as e:
            logger.error(f"Python引擎初始化异常: {e}")
            return False
    
    async def detect(self, image_data: bytes, **kwargs) -> Dict[str, Any]:
        """使用Python执行检测"""
        if not self.is_initialized:
            raise RuntimeError("Python引擎未初始化")
        
        start_time = time.time()
        
        try:
            # 导入图像处理模块
            import cv2
            import numpy as np
            
            # 解码图像
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                raise ValueError("无效的图像数据")
            
            # 执行检测
            sensitivity = kwargs.get('sensitivity', 0.8)
            detections = await self.detector.detect(image, sensitivity)
            
            # 执行跟踪
            camera_id = kwargs.get('camera_id', 'default')
            tracked_objects = await self.tracker.update(detections, camera_id)
            
            processing_time = time.time() - start_time
            self.total_detections += 1
            self.total_processing_time += processing_time
            
            # 构建结果
            height, width = image.shape[:2]
            timestamp = int(time.time() * 1000)
            
            detected_objects = []
            for obj in tracked_objects:
                detected_objects.append({
                    "object_label": "High-altitude-throwing",
                    "track_id": str(obj["track_id"]),
                    "object_box": {
                        "x": obj["bbox"][0] / width,
                        "y": obj["bbox"][1] / height,
                        "width": obj["bbox"][2] / width,
                        "height": obj["bbox"][3] / height
                    }
                })
            
            result = {
                "version": "1.0",
                "task": {
                    "model_name": "High-altitude-throwing",
                    "task_id": kwargs.get('task_id', '142')
                },
                "events": {
                    "camera_id": camera_id,
                    "timestamp": timestamp,
                    "image": {
                        "path": f"{camera_id}_{timestamp}.jpg",
                        "uri": "processed_image",
                        "width": width,
                        "height": height
                    },
                    "objects": detected_objects
                },
                "engine_info": {
                    "engine": "python",
                    "mode": "兼容模式",
                    "processing_time_ms": processing_time * 1000,
                    "gpu_accelerated": False
                }
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Python检测失败: {e}")
            raise
    
    async def cleanup(self):
        """清理Python资源"""
        if self.detector:
            await self.detector.cleanup()
        if self.tracker:
            await self.tracker.cleanup()
        self.is_initialized = False
        logger.info("Python引擎资源已清理")
    
    def get_status(self) -> Dict[str, Any]:
        """获取Python引擎状态"""
        return {
            "engine": "python",
            "initialized": self.is_initialized,
            "detector_ready": self.detector.is_ready() if self.detector else False,
            "gpu_accelerated": False,
            "concurrent_streams": 1,
            "performance_level": "standard"
        }


class SmartEngineManager:
    """智能引擎管理器"""
    
    def __init__(self):
        self.env_detector = EnvironmentDetector()
        self.current_engine: Optional[DetectionEngine] = None
        self.current_mode = None
        self.fallback_enabled = True
        self.initialization_attempts = {}
        self.max_retry_attempts = 3
    
    async def initialize(self, preferred_mode: Optional[str] = None) -> bool:
        """
        初始化引擎管理器
        
        Args:
            preferred_mode: 首选模式 ("deepstream", "python", None为自动)
        """
        logger.info("正在初始化智能引擎管理器...")
        
        # 检测环境
        env_info = self.env_detector.detect_environment()
        
        # 确定目标模式
        if preferred_mode:
            if self.env_detector.is_mode_available(preferred_mode):
                target_mode = preferred_mode
                logger.info(f"使用指定模式: {preferred_mode}")
            else:
                logger.warning(f"指定模式 {preferred_mode} 不可用，使用推荐模式")
                target_mode = env_info.recommended_mode
        else:
            target_mode = env_info.recommended_mode
            logger.info(f"使用推荐模式: {target_mode}")
        
        # 尝试初始化目标引擎
        success = await self._initialize_engine(target_mode)
        
        # 如果失败且启用了降级，尝试降级到Python模式
        if not success and self.fallback_enabled and target_mode != "python":
            logger.warning(f"{target_mode}模式初始化失败，尝试降级到Python模式")
            success = await self._initialize_engine("python")
        
        if success:
            logger.info(f"智能引擎管理器初始化成功，当前模式: {self.current_mode}")
            return True
        else:
            logger.error("智能引擎管理器初始化失败")
            return False
    
    async def _initialize_engine(self, mode: str) -> bool:
        """初始化指定模式的引擎"""
        # 检查重试次数
        if self.initialization_attempts.get(mode, 0) >= self.max_retry_attempts:
            logger.error(f"{mode}模式已达到最大重试次数")
            return False
        
        try:
            # 清理现有引擎
            if self.current_engine:
                await self.current_engine.cleanup()
                self.current_engine = None
            
            # 创建新引擎
            if mode == "deepstream":
                engine = DeepStreamEngine()
            elif mode == "python":
                engine = PythonEngine()
            else:
                logger.error(f"不支持的引擎模式: {mode}")
                return False
            
            # 初始化引擎
            if await engine.initialize():
                self.current_engine = engine
                self.current_mode = mode
                self.initialization_attempts[mode] = 0  # 重置重试计数
                return True
            else:
                self.initialization_attempts[mode] = self.initialization_attempts.get(mode, 0) + 1
                return False
                
        except Exception as e:
            logger.error(f"初始化{mode}引擎时发生异常: {e}")
            self.initialization_attempts[mode] = self.initialization_attempts.get(mode, 0) + 1
            return False
    
    async def detect(self, image_data: bytes, **kwargs) -> Dict[str, Any]:
        """执行检测"""
        if not self.current_engine:
            raise RuntimeError("引擎管理器未初始化")
        
        try:
            result = await self.current_engine.detect(image_data, **kwargs)
            return result
            
        except Exception as e:
            logger.error(f"检测失败: {e}")
            
            # 如果是DeepStream引擎失败且启用降级，尝试切换到Python模式
            if (self.current_mode == "deepstream" and 
                self.fallback_enabled and 
                self.env_detector.is_mode_available("python")):
                
                logger.warning("DeepStream检测失败，尝试降级到Python模式")
                
                if await self._initialize_engine("python"):
                    logger.info("成功降级到Python模式，重新执行检测")
                    return await self.current_engine.detect(image_data, **kwargs)
            
            raise
    
    def get_status(self) -> Dict[str, Any]:
        """获取管理器状态"""
        env_info = self.env_detector.detect_environment()
        
        status = {
            "manager_initialized": self.current_engine is not None,
            "current_mode": self.current_mode,
            "fallback_enabled": self.fallback_enabled,
            "environment": {
                "has_nvidia_gpu": env_info.has_nvidia_gpu,
                "has_cuda": env_info.has_cuda,
                "has_deepstream": env_info.has_deepstream,
                "gpu_memory_mb": env_info.gpu_memory_mb,
                "recommended_mode": env_info.recommended_mode
            },
            "available_modes": {
                "deepstream": self.env_detector.is_mode_available("deepstream"),
                "python": self.env_detector.is_mode_available("python")
            }
        }
        
        if self.current_engine:
            status["engine_status"] = self.current_engine.get_status()
            status["performance_stats"] = self.current_engine.get_performance_stats()
        
        return status
    
    async def switch_mode(self, target_mode: str) -> bool:
        """手动切换模式"""
        if not self.env_detector.is_mode_available(target_mode):
            logger.error(f"目标模式 {target_mode} 不可用")
            return False
        
        if self.current_mode == target_mode:
            logger.info(f"已经是 {target_mode} 模式")
            return True
        
        logger.info(f"切换模式: {self.current_mode} -> {target_mode}")
        return await self._initialize_engine(target_mode)
    
    async def cleanup(self):
        """清理资源"""
        if self.current_engine:
            await self.current_engine.cleanup()
            self.current_engine = None
        self.current_mode = None
        logger.info("智能引擎管理器资源已清理")
