"""
环境检测器
自动检测NVIDIA GPU、CUDA、DeepStream等环境可用性
"""

import os
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from loguru import logger


@dataclass
class EnvironmentInfo:
    """环境信息数据类"""
    has_nvidia_gpu: bool = False
    has_cuda: bool = False
    has_deepstream: bool = False
    gpu_memory_mb: int = 0
    cuda_version: str = ""
    deepstream_version: str = ""
    gpu_count: int = 0
    gpu_names: List[str] = None
    docker_gpu_support: bool = False
    recommended_mode: str = "python"  # "deepstream" or "python"
    detection_time: float = 0.0
    
    def __post_init__(self):
        if self.gpu_names is None:
            self.gpu_names = []


class EnvironmentDetector:
    """环境检测器类"""
    
    def __init__(self):
        self.detection_cache: Optional[EnvironmentInfo] = None
        self.cache_timeout = 300  # 5分钟缓存
        self.last_detection_time = 0
        
    def detect_environment(self, force_refresh: bool = False) -> EnvironmentInfo:
        """
        检测环境信息
        
        Args:
            force_refresh: 是否强制刷新缓存
            
        Returns:
            环境信息对象
        """
        current_time = time.time()
        
        # 检查缓存
        if (not force_refresh and 
            self.detection_cache and 
            current_time - self.last_detection_time < self.cache_timeout):
            logger.debug("使用缓存的环境检测结果")
            return self.detection_cache
        
        logger.info("开始环境检测...")
        start_time = time.time()
        
        env_info = EnvironmentInfo()
        
        # 检测NVIDIA GPU
        env_info.has_nvidia_gpu, gpu_info = self._detect_nvidia_gpu()
        if gpu_info:
            env_info.gpu_count = gpu_info.get('count', 0)
            env_info.gpu_names = gpu_info.get('names', [])
            env_info.gpu_memory_mb = gpu_info.get('memory_mb', 0)
        
        # 检测CUDA
        env_info.has_cuda, env_info.cuda_version = self._detect_cuda()
        
        # 检测DeepStream
        env_info.has_deepstream, env_info.deepstream_version = self._detect_deepstream()
        
        # 检测Docker GPU支持
        env_info.docker_gpu_support = self._detect_docker_gpu_support()
        
        # 确定推荐模式
        env_info.recommended_mode = self._determine_recommended_mode(env_info)
        
        # 记录检测时间
        env_info.detection_time = time.time() - start_time
        
        # 更新缓存
        self.detection_cache = env_info
        self.last_detection_time = current_time
        
        self._log_detection_results(env_info)
        
        return env_info
    
    def _detect_nvidia_gpu(self) -> Tuple[bool, Optional[Dict]]:
        """检测NVIDIA GPU"""
        try:
            # 检查nvidia-smi命令
            result = subprocess.run(
                ['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader,nounits'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                logger.debug("nvidia-smi命令执行失败")
                return False, None
            
            # 解析GPU信息
            gpu_info = {
                'count': 0,
                'names': [],
                'memory_mb': 0
            }
            
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 2:
                        gpu_name = parts[0].strip()
                        try:
                            memory_mb = int(parts[1].strip())
                            gpu_info['names'].append(gpu_name)
                            gpu_info['memory_mb'] = max(gpu_info['memory_mb'], memory_mb)
                            gpu_info['count'] += 1
                        except ValueError:
                            continue
            
            if gpu_info['count'] > 0:
                logger.info(f"检测到 {gpu_info['count']} 个NVIDIA GPU")
                return True, gpu_info
            else:
                logger.debug("未检测到NVIDIA GPU")
                return False, None
                
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError) as e:
            logger.debug(f"NVIDIA GPU检测失败: {e}")
            return False, None
    
    def _detect_cuda(self) -> Tuple[bool, str]:
        """检测CUDA环境"""
        try:
            # 方法1: 检查nvcc命令
            result = subprocess.run(
                ['nvcc', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                # 解析CUDA版本
                output = result.stdout
                for line in output.split('\n'):
                    if 'release' in line.lower():
                        parts = line.split()
                        for i, part in enumerate(parts):
                            if part.lower() == 'release':
                                if i + 1 < len(parts):
                                    version = parts[i + 1].rstrip(',')
                                    logger.info(f"检测到CUDA版本: {version}")
                                    return True, version
                
                logger.info("检测到CUDA环境")
                return True, "unknown"
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        # 方法2: 检查CUDA库文件
        cuda_paths = [
            '/usr/local/cuda',
            '/opt/cuda',
            '/usr/cuda'
        ]
        
        for cuda_path in cuda_paths:
            if Path(cuda_path).exists():
                version_file = Path(cuda_path) / 'version.txt'
                if version_file.exists():
                    try:
                        with open(version_file, 'r') as f:
                            content = f.read()
                            if 'CUDA Version' in content:
                                version = content.split('CUDA Version')[1].strip()
                                logger.info(f"检测到CUDA版本: {version}")
                                return True, version
                    except:
                        pass
                
                logger.info("检测到CUDA安装目录")
                return True, "unknown"
        
        # 方法3: 检查环境变量
        cuda_home = os.environ.get('CUDA_HOME') or os.environ.get('CUDA_PATH')
        if cuda_home and Path(cuda_home).exists():
            logger.info("检测到CUDA环境变量")
            return True, "unknown"
        
        logger.debug("未检测到CUDA环境")
        return False, ""
    
    def _detect_deepstream(self) -> Tuple[bool, str]:
        """检测DeepStream SDK"""
        try:
            # 检查DeepStream安装路径
            deepstream_paths = [
                '/opt/nvidia/deepstream/deepstream',
                '/usr/local/deepstream',
                '/opt/deepstream'
            ]
            
            for ds_path in deepstream_paths:
                ds_dir = Path(ds_path)
                if ds_dir.exists():
                    # 检查关键文件
                    lib_dir = ds_dir / 'lib'
                    bin_dir = ds_dir / 'bin'
                    
                    if lib_dir.exists() and bin_dir.exists():
                        # 尝试获取版本信息
                        version_file = ds_dir / 'VERSION'
                        if version_file.exists():
                            try:
                                with open(version_file, 'r') as f:
                                    version = f.read().strip()
                                    logger.info(f"检测到DeepStream版本: {version}")
                                    return True, version
                            except:
                                pass
                        
                        logger.info(f"检测到DeepStream安装: {ds_path}")
                        return True, "unknown"
            
            # 检查环境变量
            deepstream_path = os.environ.get('DEEPSTREAM_PATH')
            if deepstream_path and Path(deepstream_path).exists():
                logger.info("检测到DeepStream环境变量")
                return True, "unknown"
            
            logger.debug("未检测到DeepStream SDK")
            return False, ""
            
        except Exception as e:
            logger.debug(f"DeepStream检测失败: {e}")
            return False, ""
    
    def _detect_docker_gpu_support(self) -> bool:
        """检测Docker GPU支持"""
        try:
            # 检查是否在Docker容器中
            if Path('/.dockerenv').exists():
                # 在容器中，检查GPU设备
                gpu_devices = list(Path('/dev').glob('nvidia*'))
                if gpu_devices:
                    logger.info("检测到Docker GPU支持")
                    return True
            
            # 检查Docker命令和nvidia-docker
            result = subprocess.run(
                ['docker', '--version'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                # 检查nvidia-container-runtime
                result = subprocess.run(
                    ['docker', 'info'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0 and 'nvidia' in result.stdout.lower():
                    logger.info("检测到Docker NVIDIA容器运行时")
                    return True
            
            logger.debug("未检测到Docker GPU支持")
            return False
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError) as e:
            logger.debug(f"Docker GPU支持检测失败: {e}")
            return False
    
    def _determine_recommended_mode(self, env_info: EnvironmentInfo) -> str:
        """确定推荐的运行模式"""
        # 检查DeepStream模式的必要条件
        if (env_info.has_nvidia_gpu and 
            env_info.has_cuda and 
            env_info.has_deepstream and
            env_info.gpu_memory_mb >= 2048):  # 至少2GB GPU内存
            
            logger.info("推荐使用DeepStream模式 (高性能)")
            return "deepstream"
        
        # 检查是否有GPU但缺少其他组件
        if env_info.has_nvidia_gpu:
            missing_components = []
            if not env_info.has_cuda:
                missing_components.append("CUDA")
            if not env_info.has_deepstream:
                missing_components.append("DeepStream")
            if env_info.gpu_memory_mb < 2048:
                missing_components.append(f"足够的GPU内存 (当前: {env_info.gpu_memory_mb}MB)")
            
            if missing_components:
                logger.warning(f"检测到GPU但缺少组件: {', '.join(missing_components)}")
                logger.info("降级到Python模式 (兼容)")
        else:
            logger.info("未检测到NVIDIA GPU，使用Python模式 (兼容)")
        
        return "python"
    
    def _log_detection_results(self, env_info: EnvironmentInfo):
        """记录检测结果"""
        logger.info("=" * 50)
        logger.info("环境检测结果:")
        logger.info(f"  NVIDIA GPU: {'✅' if env_info.has_nvidia_gpu else '❌'}")
        if env_info.has_nvidia_gpu:
            logger.info(f"    GPU数量: {env_info.gpu_count}")
            logger.info(f"    GPU型号: {', '.join(env_info.gpu_names)}")
            logger.info(f"    GPU内存: {env_info.gpu_memory_mb} MB")
        
        logger.info(f"  CUDA: {'✅' if env_info.has_cuda else '❌'}")
        if env_info.has_cuda and env_info.cuda_version:
            logger.info(f"    版本: {env_info.cuda_version}")
        
        logger.info(f"  DeepStream: {'✅' if env_info.has_deepstream else '❌'}")
        if env_info.has_deepstream and env_info.deepstream_version:
            logger.info(f"    版本: {env_info.deepstream_version}")
        
        logger.info(f"  Docker GPU: {'✅' if env_info.docker_gpu_support else '❌'}")
        logger.info(f"  推荐模式: {env_info.recommended_mode.upper()}")
        logger.info(f"  检测耗时: {env_info.detection_time:.2f}秒")
        logger.info("=" * 50)
    
    def get_mode_capabilities(self, mode: str) -> Dict:
        """获取指定模式的能力信息"""
        if mode == "deepstream":
            return {
                "name": "DeepStream高性能模式",
                "performance": "高性能",
                "concurrent_streams": 15,
                "gpu_acceleration": True,
                "processing_fps": "60+",
                "detection_accuracy": ">90%",
                "memory_usage": "~4GB GPU",
                "requirements": ["NVIDIA GPU", "CUDA", "DeepStream SDK"]
            }
        else:
            return {
                "name": "Python兼容模式", 
                "performance": "标准性能",
                "concurrent_streams": 1,
                "gpu_acceleration": False,
                "processing_fps": "~30",
                "detection_accuracy": ">85%",
                "memory_usage": "~2GB RAM",
                "requirements": ["Python 3.8+", "OpenCV"]
            }
    
    def is_mode_available(self, mode: str) -> bool:
        """检查指定模式是否可用"""
        env_info = self.detect_environment()
        
        if mode == "deepstream":
            return (env_info.has_nvidia_gpu and 
                   env_info.has_cuda and 
                   env_info.has_deepstream and
                   env_info.gpu_memory_mb >= 2048)
        elif mode == "python":
            return True  # Python模式总是可用
        else:
            return False
