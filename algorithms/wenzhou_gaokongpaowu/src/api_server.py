"""
温州高空抛物检测算法 API 服务器
智能自适应算法包：自动检测环境并选择最优的执行模式（DeepStream/Python）
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from loguru import logger
import aiofiles

from .environment_detector import EnvironmentDetector
from .engine_manager import SmartEngineManager

# 配置日志
logger.add("logs/smart_api_server.log", rotation="1 day", retention="7 days", level="INFO")

app = FastAPI(
    title="温州高空抛物检测算法",
    description="智能自适应算法包：自动检测环境并选择最优执行模式的高空抛物行为检测与报警系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局变量
engine_manager: Optional[SmartEngineManager] = None
env_detector: Optional[EnvironmentDetector] = None
start_time = time.time()

# 数据模型
class HealthResponse(BaseModel):
    status: str = "healthy"
    timestamp: str
    version: str = "1.0.0"
    uptime_seconds: float
    current_mode: Optional[str] = None
    mode_info: Dict[str, Any] = Field(default_factory=dict)
    environment: Dict[str, Any] = Field(default_factory=dict)
    performance_stats: Dict[str, Any] = Field(default_factory=dict)

class DetectionRequest(BaseModel):
    camera_id: str = Field(default="default", description="摄像头ID")
    task_id: str = Field(default="142", description="任务ID")
    sensitivity: float = Field(default=0.8, description="检测灵敏度 (0.0-1.0)")

class ObjectBox(BaseModel):
    x: float = Field(..., description="边界框左上角X坐标 (归一化)")
    y: float = Field(..., description="边界框左上角Y坐标 (归一化)")
    width: float = Field(..., description="边界框宽度 (归一化)")
    height: float = Field(..., description="边界框高度 (归一化)")

class DetectedObject(BaseModel):
    object_label: str = Field(..., description="检测对象标签")
    track_id: str = Field(..., description="跟踪ID")
    object_box: ObjectBox = Field(..., description="边界框信息")

class ImageInfo(BaseModel):
    path: str = Field(..., description="图像保存路径")
    uri: str = Field(..., description="原始图像URI")
    width: int = Field(..., description="图像宽度")
    height: int = Field(..., description="图像高度")

class EventInfo(BaseModel):
    camera_id: str = Field(..., description="摄像头ID")
    timestamp: int = Field(..., description="时间戳 (毫秒)")
    image: ImageInfo = Field(..., description="图像信息")
    objects: List[DetectedObject] = Field(..., description="检测到的对象列表")

class DetectionResponse(BaseModel):
    version: str = "1.0"
    task: Dict[str, str] = Field(..., description="任务信息")
    events: EventInfo = Field(..., description="事件信息")
    processing_time_ms: float = Field(..., description="处理时间 (毫秒)")
    engine_info: Dict[str, Any] = Field(..., description="引擎信息")

class ModeInfo(BaseModel):
    current_mode: str
    available_modes: List[str]
    mode_capabilities: Dict[str, Any]

class ModeSwitchRequest(BaseModel):
    target_mode: str = Field(..., description="目标模式 (deepstream/python)")

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global engine_manager, env_detector
    
    logger.info("正在启动温州高空抛物检测算法服务 (智能版本)...")
    
    try:
        # 创建环境检测器
        env_detector = EnvironmentDetector()
        
        # 创建智能引擎管理器
        engine_manager = SmartEngineManager()
        
        # 初始化引擎管理器（自动选择最优模式）
        if await engine_manager.initialize():
            logger.info("智能引擎管理器初始化成功")
            
            # 记录当前模式信息
            status = engine_manager.get_status()
            current_mode = status.get('current_mode', 'unknown')
            env_info = status.get('environment', {})
            
            logger.info(f"当前运行模式: {current_mode.upper()}")
            logger.info(f"GPU可用: {'是' if env_info.get('has_nvidia_gpu') else '否'}")
            logger.info(f"CUDA可用: {'是' if env_info.get('has_cuda') else '否'}")
            logger.info(f"DeepStream可用: {'是' if env_info.get('has_deepstream') else '否'}")
            
        else:
            logger.error("智能引擎管理器初始化失败")
            raise RuntimeError("引擎管理器初始化失败")
        
        # 创建必要的目录
        from pathlib import Path
        Path("logs").mkdir(exist_ok=True)
        Path("data/images").mkdir(parents=True, exist_ok=True)
        Path("data/temp").mkdir(parents=True, exist_ok=True)
        
        logger.info("温州高空抛物检测算法服务启动成功 (智能版本)")
        
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    logger.info("正在关闭温州高空抛物检测算法服务...")
    
    global engine_manager
    if engine_manager:
        await engine_manager.cleanup()
    
    logger.info("服务已安全关闭")

@app.get("/api/v1/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口"""
    global engine_manager, env_detector, start_time
    
    current_time = datetime.now().isoformat()
    uptime = time.time() - start_time
    
    if not engine_manager:
        return HealthResponse(
            status="unhealthy",
            timestamp=current_time,
            uptime_seconds=uptime,
            current_mode=None
        )
    
    # 获取引擎管理器状态
    status = engine_manager.get_status()
    
    # 获取当前模式的能力信息
    current_mode = status.get('current_mode')
    mode_capabilities = {}
    if current_mode and env_detector:
        mode_capabilities = env_detector.get_mode_capabilities(current_mode)
    
    return HealthResponse(
        timestamp=current_time,
        uptime_seconds=uptime,
        current_mode=current_mode,
        mode_info=mode_capabilities,
        environment=status.get('environment', {}),
        performance_stats=status.get('performance_stats', {})
    )

@app.post("/api/v1/detect", response_model=DetectionResponse)
async def detect_image(
    file: UploadFile = File(..., description="上传的图像文件"),
    camera_id: str = "default",
    task_id: str = "142",
    sensitivity: float = 0.8
):
    """单帧图像检测接口"""
    global engine_manager
    
    if not engine_manager:
        raise HTTPException(status_code=503, detail="引擎管理器未初始化")
    
    start_time_ms = time.time() * 1000
    
    try:
        # 读取上传的图像
        contents = await file.read()
        
        if not contents:
            raise HTTPException(status_code=400, detail="空的图像文件")
        
        # 使用智能引擎管理器执行检测
        result = await engine_manager.detect(
            contents,
            camera_id=camera_id,
            task_id=task_id,
            sensitivity=sensitivity
        )
        
        # 计算总处理时间
        total_processing_time = (time.time() * 1000) - start_time_ms
        
        # 更新处理时间
        result["processing_time_ms"] = total_processing_time
        
        # 构建响应
        response = DetectionResponse(
            task=result.get("task", {"model_name": "High-altitude-throwing", "task_id": task_id}),
            events=EventInfo(
                camera_id=result["events"]["camera_id"],
                timestamp=result["events"]["timestamp"],
                image=ImageInfo(
                    path=result["events"]["image"]["path"],
                    uri=result["events"]["image"]["uri"],
                    width=result["events"]["image"]["width"],
                    height=result["events"]["image"]["height"]
                ),
                objects=[
                    DetectedObject(
                        object_label=obj["object_label"],
                        track_id=obj["track_id"],
                        object_box=ObjectBox(
                            x=obj["object_box"]["x"],
                            y=obj["object_box"]["y"],
                            width=obj["object_box"]["width"],
                            height=obj["object_box"]["height"]
                        )
                    )
                    for obj in result["events"]["objects"]
                ]
            ),
            processing_time_ms=total_processing_time,
            engine_info=result.get("engine_info", {})
        )
        
        logger.info(
            f"检测完成: {len(response.events.objects)} 个对象, "
            f"处理时间: {total_processing_time:.2f}ms, "
            f"引擎: {result.get('engine_info', {}).get('engine', 'unknown')}"
        )
        
        return response
        
    except Exception as e:
        logger.error(f"检测过程中发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"检测失败: {str(e)}")

@app.get("/api/v1/mode", response_model=ModeInfo)
async def get_mode_info():
    """获取模式信息"""
    global engine_manager, env_detector
    
    if not engine_manager or not env_detector:
        raise HTTPException(status_code=503, detail="服务未初始化")
    
    status = engine_manager.get_status()
    current_mode = status.get('current_mode', 'unknown')
    
    available_modes = []
    if status.get('available_modes', {}).get('deepstream'):
        available_modes.append('deepstream')
    if status.get('available_modes', {}).get('python'):
        available_modes.append('python')
    
    mode_capabilities = env_detector.get_mode_capabilities(current_mode)
    
    return ModeInfo(
        current_mode=current_mode,
        available_modes=available_modes,
        mode_capabilities=mode_capabilities
    )

@app.post("/api/v1/mode/switch")
async def switch_mode(request: ModeSwitchRequest):
    """手动切换模式"""
    global engine_manager
    
    if not engine_manager:
        raise HTTPException(status_code=503, detail="引擎管理器未初始化")
    
    target_mode = request.target_mode.lower()
    
    if target_mode not in ['deepstream', 'python']:
        raise HTTPException(status_code=400, detail="无效的目标模式")
    
    try:
        success = await engine_manager.switch_mode(target_mode)
        
        if success:
            logger.info(f"成功切换到 {target_mode} 模式")
            return {"success": True, "message": f"成功切换到 {target_mode} 模式"}
        else:
            logger.error(f"切换到 {target_mode} 模式失败")
            raise HTTPException(status_code=500, detail=f"切换到 {target_mode} 模式失败")
            
    except Exception as e:
        logger.error(f"模式切换异常: {e}")
        raise HTTPException(status_code=500, detail=f"模式切换失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)
