# 温州高空抛物检测算法 - 智能版本 Docker镜像
# 支持自动检测环境并选择最优执行模式

# 阶段1: 基础环境 (支持GPU和CPU)
FROM nvidia/cuda:11.8-devel-ubuntu20.04 as base

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 安装基础系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    pkg-config \
    python3 \
    python3-pip \
    python3-dev \
    git \
    wget \
    curl \
    vim \
    net-tools \
    software-properties-common \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
RUN python3 -m pip install --upgrade pip && \
    pip3 install \
    fastapi==0.104.1 \
    uvicorn[standard]==0.24.2 \
    pydantic==2.5.0 \
    python-multipart==0.0.6 \
    aiofiles==23.2.1 \
    loguru==0.7.2 \
    requests==2.31.0 \
    numpy==1.24.3 \
    opencv-python==******** \
    Pillow==10.1.0 \
    scipy==1.11.1 \
    scikit-image==0.21.0 \
    filterpy==1.4.5

# 阶段2: DeepStream环境 (仅在GPU可用时)
FROM nvcr.io/nvidia/deepstream:6.1-triton as deepstream-stage

# 复制基础环境
COPY --from=base /usr/local /usr/local
COPY --from=base /usr/bin/python3 /usr/bin/python3

# 设置DeepStream环境变量
ENV DEEPSTREAM_PATH=/opt/nvidia/deepstream/deepstream
ENV LD_LIBRARY_PATH=${DEEPSTREAM_PATH}/lib:${LD_LIBRARY_PATH}
ENV GST_PLUGIN_PATH=${DEEPSTREAM_PATH}/lib/gst-plugins:${GST_PLUGIN_PATH}

# 安装额外的DeepStream依赖
RUN apt-get update && apt-get install -y \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    libgstreamer-plugins-good1.0-dev \
    libgstreamer-plugins-bad1.0-dev \
    libgstreamer-rtsp-server-1.0-dev \
    libglib2.0-dev \
    libjson-glib-dev \
    uuid-dev \
    libssl-dev \
    libcurl4-openssl-dev \
    libyaml-cpp-dev \
    librdkafka-dev \
    && rm -rf /var/lib/apt/lists/*

# 阶段3: 最终镜像 (智能选择)
FROM base as final

# 设置工作目录
WORKDIR /app

# 复制应用代码
COPY src/ /app/src/
COPY engines/ /app/engines/
COPY config/ /app/config/
COPY scripts/ /app/scripts/
COPY pyproject.toml /app/
COPY README.md /app/

# 创建必要的目录
RUN mkdir -p /app/logs \
    /app/data/images \
    /app/data/temp \
    /app/data/videos \
    /app/output \
    /app/models

# 复制DeepStream相关文件 (如果存在)
COPY --from=deepstream-stage /opt/nvidia/deepstream/deepstream /opt/nvidia/deepstream/deepstream 2>/dev/null || true

# 复制启动脚本
COPY scripts/smart_start.sh /app/smart_start.sh
RUN chmod +x /app/smart_start.sh

# 创建非root用户
RUN useradd -m -u 1000 appuser && \
    chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD curl -f http://localhost:8005/api/v1/health || exit 1

# 暴露端口
EXPOSE 8005

# 智能启动命令
CMD ["/app/smart_start.sh"]
