# 温州高空抛物检测算法

**🧠 智能自适应算法包：自动检测环境并选择最优执行模式的高空抛物行为检测与报警系统**

## 🎯 核心特性

### 🔄 智能模式切换
- **自动环境检测**: 启动时自动检测NVIDIA GPU、CUDA、DeepStream等环境
- **智能模式选择**: 根据环境自动选择最优执行模式
- **无缝降级**: GPU环境不可用时自动降级到CPU兼容模式
- **运行时切换**: 支持运行时手动切换执行模式

### 🚀 双引擎架构
- **DeepStream引擎**: GPU硬件加速，支持15路并发，60+ FPS处理
- **Python引擎**: CPU兼容模式，支持任意环境部署
- **统一API**: 两种引擎提供完全一致的API接口
- **透明切换**: 用户无需关心底层实现差异

### 📊 性能自适应
- **环境感知**: 实时监控GPU状态和资源使用
- **性能优化**: 根据硬件能力自动调整处理参数
- **故障恢复**: DeepStream故障时自动降级到Python模式
- **负载均衡**: 智能分配计算资源

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    统一API接口层                              │
│              GET /health | POST /detect                    │
└─────────────────────────┬───────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────┐
│                  智能引擎管理器                               │
│              环境检测 | 模式选择 | 故障恢复                    │
└─────────────┬───────────────────────────────┬───────────────┘
              │                               │
    ┌─────────▼─────────┐           ┌─────────▼─────────┐
    │  DeepStream引擎   │           │   Python引擎      │
    │                   │           │                   │
    │ • GPU硬件加速     │           │ • CPU兼容处理     │
    │ • 15路并发        │           │ • 单路处理        │
    │ • 60+ FPS         │           │ • ~30 FPS         │
    │ • >90% 精度       │           │ • >85% 精度       │
    └───────────────────┘           └───────────────────┘
```

## 🚀 快速开始

### 环境要求

**最低要求**:
- Python 3.8+
- 2GB RAM
- Docker (可选)

**GPU加速要求**:
- NVIDIA GPU (计算能力 >= 6.0)
- NVIDIA Driver >= 470.x
- CUDA >= 11.4
- DeepStream SDK 6.1+

### 一键部署

```bash
# 1. 构建智能镜像
docker build -t wenzhou-gaokongpaowu-smart:1.0.0 .

# 2. GPU环境运行 (自动使用DeepStream模式)
docker run -d \
  --name wenzhou-gaokongpaowu-smart \
  --gpus all \
  -p 8005:8005 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  wenzhou-gaokongpaowu-smart:1.0.0

# 3. CPU环境运行 (自动使用Python模式)
docker run -d \
  --name wenzhou-gaokongpaowu-smart \
  -p 8005:8005 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  wenzhou-gaokongpaowu-smart:1.0.0
```

### 本地开发

```bash
# 1. 进入项目目录
cd algorithms/wenzhou_gaokongpaowu_smart

# 2. 安装依赖
pip install -e .

# 3. 启动服务 (自动检测环境)
python -m uvicorn src.api_server:app --host 0.0.0.0 --port 8005

# 4. 访问API文档
open http://localhost:8005/docs
```

## 📡 API接口

### 健康检查 (增强版)
```http
GET /api/v1/health
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-01T12:00:00",
  "version": "1.0.0",
  "uptime_seconds": 3600.5,
  "current_mode": "deepstream",
  "mode_info": {
    "name": "DeepStream高性能模式",
    "performance": "高性能",
    "concurrent_streams": 15,
    "gpu_acceleration": true,
    "processing_fps": "60+",
    "detection_accuracy": ">90%"
  },
  "environment": {
    "has_nvidia_gpu": true,
    "has_cuda": true,
    "has_deepstream": true,
    "gpu_memory_mb": 8192,
    "recommended_mode": "deepstream"
  },
  "performance_stats": {
    "total_detections": 1250,
    "average_processing_time": 45.2,
    "initialization_time": 2.3
  }
}
```

### 图像检测 (统一接口)
```http
POST /api/v1/detect
Content-Type: multipart/form-data

file: <image_file>
camera_id: "camera_001"
task_id: "142"
sensitivity: 0.8
```

**响应示例**:
```json
{
  "version": "1.0",
  "task": {
    "model_name": "High-altitude-throwing",
    "task_id": "142"
  },
  "events": {
    "camera_id": "camera_001",
    "timestamp": 1704110400000,
    "image": {
      "path": "camera_001_1704110400000.jpg",
      "uri": "file://input_image.jpg",
      "width": 1920,
      "height": 1080
    },
    "objects": [
      {
        "object_label": "High-altitude-throwing",
        "track_id": "1",
        "object_box": {
          "x": 0.653,
          "y": 0.175,
          "width": 0.025,
          "height": 0.028
        }
      }
    ]
  },
  "processing_time_ms": 45.2,
  "engine_info": {
    "engine": "deepstream",
    "mode": "高性能模式",
    "processing_time_ms": 45.2,
    "gpu_accelerated": true
  }
}
```

### 模式信息查询
```http
GET /api/v1/mode
```

### 手动模式切换
```http
POST /api/v1/mode/switch
Content-Type: application/json

{
  "target_mode": "python"
}
```

## ⚙️ 智能特性

### 自动环境检测
- **GPU检测**: 自动检测NVIDIA GPU型号、内存、驱动版本
- **CUDA检测**: 检测CUDA运行时和开发工具包
- **DeepStream检测**: 检测DeepStream SDK安装和版本
- **Docker检测**: 检测Docker GPU支持和容器环境

### 智能模式选择
- **性能优先**: GPU环境完整时自动选择DeepStream模式
- **兼容优先**: 环境不满足时自动降级到Python模式
- **故障恢复**: DeepStream故障时自动切换到Python模式
- **手动控制**: 支持用户手动指定运行模式

### 性能监控
- **实时统计**: 记录检测次数、处理时间、成功率
- **资源监控**: 监控GPU使用率、内存占用
- **性能分析**: 提供详细的性能分析报告
- **告警机制**: 性能异常时自动告警

## 📊 性能对比

| 指标 | DeepStream模式 | Python模式 | 智能选择 |
|------|---------------|------------|----------|
| **处理速度** | 60+ FPS | ~30 FPS | **自动最优** |
| **并发能力** | 15路 | 1路 | **自动最优** |
| **检测精度** | >90% | >85% | **自动最优** |
| **GPU加速** | ✅ | ❌ | **自动检测** |
| **环境要求** | 严格 | 宽松 | **自动适配** |
| **部署复杂度** | 高 | 低 | **自动简化** |

## 🔧 配置选项

### 环境变量
```bash
# 强制指定模式
PREFERRED_MODE=deepstream  # 或 python

# 禁用自动降级
FALLBACK_ENABLED=false

# 检测缓存时间
DETECTION_CACHE_TIMEOUT=300

# 最大重试次数
MAX_RETRY_ATTEMPTS=3
```

### 配置文件
```yaml
# config/smart_config.yaml
detection:
  preferred_mode: auto  # auto, deepstream, python
  fallback_enabled: true
  cache_timeout: 300

performance:
  max_retry_attempts: 3
  health_check_interval: 30
  performance_monitoring: true

logging:
  level: INFO
  rotation: "1 day"
  retention: "7 days"
```

## 🧪 测试验证

### 功能测试
```bash
# 运行完整测试套件
pytest tests/ -v

# 测试环境检测
pytest tests/test_environment_detector.py -v

# 测试智能引擎管理器
pytest tests/test_engine_manager.py -v

# 测试API接口
pytest tests/test_api_server.py -v
```

### 性能测试
```bash
# GPU模式性能测试
pytest tests/test_performance.py::test_deepstream_performance -v

# CPU模式性能测试
pytest tests/test_performance.py::test_python_performance -v

# 模式切换测试
pytest tests/test_smart_switching.py -v
```

## 📈 版本历史

### v1.0.0 (2025-01-01)
- ✨ 智能环境检测和模式选择
- 🚀 双引擎架构 (DeepStream + Python)
- 🔄 运行时模式切换
- 📊 性能监控和统计
- 🛡️ 故障恢复和自动降级
- 📡 统一API接口
- 🐳 智能Docker部署

## 🎯 使用建议

### 部署策略
- **生产环境**: 使用GPU服务器，自动获得DeepStream高性能
- **开发环境**: 任意环境部署，自动适配最优模式
- **测试环境**: CPU环境即可，确保功能完整性
- **边缘设备**: 根据硬件能力自动选择合适模式

### 最佳实践
- **让系统自动选择**: 不要手动指定模式，让智能检测发挥作用
- **监控性能指标**: 定期检查 `/api/v1/health` 了解系统状态
- **合理配置资源**: 确保GPU内存足够，避免频繁模式切换
- **日志分析**: 关注模式切换日志，优化部署环境

---

**🎉 智能算法包完成！**

这是一个真正智能的算法包，能够：
- 🧠 **自动适应环境**: 无需手动配置，自动选择最优模式
- 🚀 **性能最大化**: GPU环境下自动获得最佳性能
- 🛡️ **高可用性**: 故障时自动降级，确保服务不中断
- 📊 **透明监控**: 提供详细的性能和环境信息
- 🔄 **灵活切换**: 支持运行时模式切换和配置调整

**一个算法包，适配所有环境！**
