# 统一AI算法管理平台开发指南

## 🚀 快速开始

### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- uv (Python包管理器)
- NVIDIA GPU + CUDA (可选，用于GPU加速算法)

### 完整部署流程
```bash
# 1. 构建管理平台
./build-deployment-package.sh

# 2. 构建算法包
./build-algorithm-packages.sh

# 3. 部署系统
cd separated-deployment-system
./deploy.sh
```

### 访问地址
- **管理平台**: http://localhost
- **API服务**: http://localhost:8100
- **API文档**: http://localhost:8100/docs

## 🛠️ 算法开发

### 使用算法管理工具
```bash
cd algorithms/_scripts
./manage-algorithms.sh
```

**主要功能**：
1. **添加新算法包** - 自动创建标准结构和模板文件
2. **删除算法包** - 完全清除算法并释放端口
3. **列出现有算法** - 显示所有算法的状态信息
4. **查看端口分配状态** - 详细的端口使用统计
5. **端口管理和清理** - 检测清理孤立端口，更新映射表
6. **构建算法包** - 集成构建功能，支持单个和批量构建

### 算法开发流程
```bash
# 1. 创建算法包
cd algorithms/_scripts
./manage-algorithms.sh  # 选择"1. 添加新算法包"

# 2. 开发算法逻辑
cd algorithms/your-algorithm
# 编辑 src/api_server.py
# 添加依赖到 pyproject.toml

# 3. 本地测试
uv sync
uv run uvicorn src.api_server:app --reload

# 4. 构建算法包
cd algorithms/_scripts
./manage-algorithms.sh  # 选择"6. 构建算法包"

# 5. 部署算法包
cd separated-deployment-system
./deploy.sh
```

### 算法包标准结构
```
your-algorithm/
├── Dockerfile                      # 容器构建文件
├── pyproject.toml                  # 项目配置文件
├── src/
│   ├── __init__.py
│   └── api_server.py              # FastAPI应用
└── README.md                      # 算法说明文档
```

### 智能算法包结构 (推荐)
```
smart-algorithm/
├── Dockerfile                      # 智能多阶段构建
├── pyproject.toml                  # 项目配置
├── src/
│   ├── api_server.py              # 统一API服务器
│   ├── environment_detector.py    # 环境检测器
│   └── engine_manager.py          # 智能引擎管理器
├── engines/
│   ├── python_engine.py           # CPU兼容引擎
│   ├── deepstream_engine.py       # GPU加速引擎
│   └── core/                      # 核心算法模块
├── scripts/
│   └── smart_start.sh             # 智能启动脚本
└── README.md                      # 完整文档
```

### 必需API接口
```python
from fastapi import FastAPI

app = FastAPI()

@app.get("/api/v1/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": "2025-01-01T12:00:00",
        "version": "1.0.0"
    }

@app.post("/api/v1/detect")
async def detect(file: UploadFile = File(...)):
    # 检测算法逻辑
    return {
        "version": "1.0",
        "events": {
            "objects": [],
            "timestamp": int(time.time() * 1000)
        }
    }
```

## 🏗️ 系统架构

### 项目结构
```
algorithm_platform/
├── algorithm-platform-manager/     # 管理平台源码
├── algorithms/                     # 算法项目
│   ├── _scripts/                   # 算法管理工具
│   │   ├── manage-algorithms.sh    # 主管理脚本
│   │   └── cleanup-orphaned-ports.py # 端口清理工具
│   ├── _standards/                 # 开发规范文档
│   ├── wenzhou_gaokongpaowu/       # 温州高空抛物检测算法 (智能版本)
│   ├── wenzhou_trash_detect/       # 温州垃圾检测算法
│   └── [other-algorithms]/         # 其他算法包目录
├── separated-deployment-system/    # 独立部署系统
├── build-deployment-package.sh     # 平台构建脚本
├── build-algorithm-packages.sh     # 算法构建脚本
└── port-registry.json              # 端口分配注册表
```

### 智能算法包架构
```
智能算法包 (wenzhou_gaokongpaowu)
├── 🧠 环境检测器
│   ├── GPU检测 (nvidia-smi)
│   ├── CUDA检测 (nvcc, 库文件)
│   ├── DeepStream检测 (SDK安装)
│   └── Docker GPU支持检测
├── ⚡ 双引擎架构
│   ├── DeepStream引擎 (GPU高性能: 60+ FPS, 15路并发)
│   └── Python引擎 (CPU兼容: 30 FPS, 1路处理)
├── 🚀 智能引擎管理器
│   ├── 自动模式选择
│   ├── 故障自动恢复
│   └── 运行时模式切换
└── 📡 统一API接口
    ├── GET /api/v1/health (增强版)
    ├── POST /api/v1/detect (统一接口)
    ├── GET /api/v1/mode (模式信息)
    └── POST /api/v1/mode/switch (模式切换)
```

### 网络配置
- 统一网络: `algorithm-network`
- 统一项目: `algorithm-platform`
- 管理平台: 端口80, 8100
- 算法容器: 端口8002-8100 (动态分配)

## 🔌 端口管理系统

### 端口分配规范
- **端口范围**: 8002-8099 (98个可用端口)
- **管理平台端口**: 80 (前端), 8100 (后端API)
- **预留端口**: 8100 (管理平台后端)
- **分配策略**: 自动分配下一个可用端口

### 当前端口分配状态
**实时查看端口分配**：
```bash
cd algorithms/_scripts
./manage-algorithms.sh
# 选择 "4. 查看端口分配状态"
```

**当前活跃算法端口**：
- **8002**: wenzhou_trash_detect (温州垃圾检测算法)
- **8003**: wenzhou_trash_detect_v2 (温州垃圾检测算法v2)
- **8004**: wenzhou_trash_detect_v3 (温州垃圾检测算法v3)
- **8005**: wenzhou_gaokongpaowu (温州高空抛物检测算法 - 智能版本)

**直接查看端口注册表**：
```bash
cat port-registry.json
```

### 智能算法包端口特性
温州高空抛物检测算法 (端口8005) 具有以下智能特性：
- **自动环境检测**: 启动时检测GPU/CUDA/DeepStream环境
- **智能模式选择**: 根据环境自动选择DeepStream或Python模式
- **双引擎架构**: 一个端口支持两种执行模式
- **运行时切换**: 支持通过API手动切换模式
- **故障恢复**: DeepStream失败时自动降级到Python模式

### 端口管理操作
```bash
cd algorithms/_scripts
./manage-algorithms.sh
# 选择"5. 端口管理和清理"
```

### 端口注册表文件
- **位置**: `port-registry.json` (项目根目录)
- **格式**: JSON格式，包含端口分配、元数据和统计信息
- **更新**: 算法添加/删除时自动更新
- **查看**: 使用管理脚本或直接读取文件

## 📦 构建和部署

### 构建流程
```bash
# 1. 构建管理平台
./build-deployment-package.sh

# 2. 构建算法包
./build-algorithm-packages.sh

# 3. 部署系统
cd separated-deployment-system
./deploy.sh
```

### 部署包结构
```
separated-deployment-system/
├── algorithm-packages/              # 算法包目录
│   ├── {name}-algorithm-package.tar.gz
│   └── {name}-manifest.json
├── platform-package/               # 平台包目录
│   └── algorithm-platform-deploy-package/
└── deploy.sh                       # 部署脚本
```

## 📦 清单文件格式 (v2.0)

### 标准清单文件结构
```json
{
  "manifest_version": "2.0",
  "generated_at": "2025-01-01T00:00:00Z",
  "algorithm": {
    "name": "algorithm-name",
    "display_name": "算法显示名称",
    "version": "1.0.0",
    "port": 8002,
    "type": "docker-compose"
  },
  "package": {
    "file": "algorithm-name-algorithm-package.tar.gz",
    "format": "tar.gz",
    "size": "705M",
    "size_bytes": 727923313,
    "created_at": "2025-01-01T00:00:00Z"
  },
  "deployment": {
    "network": "algorithm-network",
    "health_check": "/api/v1/health",
    "startup_timeout": 60,
    "container_name": "algorithm-name-1.0.0",
    "project_name": "algorithm-platform"
  },
  "requirements": {
    "docker_version": "20.10+",
    "memory_mb": 4096,
    "disk_mb": 2048
  },
  "metadata": {
    "build_system": "algorithm-platform-builder",
    "isolation": "independent"
  }
}
```

## 📡 API参考

### 管理平台API
- `GET /api/health` - 平台健康检查
- `GET /api/containers` - 容器列表
- `ws://localhost:8100/ws` - WebSocket通信

### 算法API标准
- `GET /api/v1/health` - 健康检查 (必需)
- `POST /api/v1/detect` - 检测接口
- `POST /api/v1/classify` - 分类接口
- `POST /api/v1/predict` - 通用预测接口

### 智能算法包API (wenzhou_gaokongpaowu)

#### 增强健康检查
```http
GET /api/v1/health
```
**响应示例**：
```json
{
  "status": "healthy",
  "timestamp": "2025-01-01T12:00:00",
  "version": "1.0.0",
  "uptime_seconds": 3600.5,
  "current_mode": "deepstream",
  "mode_info": {
    "name": "DeepStream高性能模式",
    "performance": "高性能",
    "concurrent_streams": 15,
    "gpu_acceleration": true,
    "processing_fps": "60+"
  },
  "environment": {
    "has_nvidia_gpu": true,
    "has_cuda": true,
    "has_deepstream": true,
    "gpu_memory_mb": 8192,
    "recommended_mode": "deepstream"
  }
}
```

#### 统一检测接口
```http
POST /api/v1/detect
Content-Type: multipart/form-data

file: <image_file>
camera_id: "camera_001"
task_id: "142"
sensitivity: 0.8
```
**响应示例**：
```json
{
  "version": "1.0",
  "task": {
    "model_name": "High-altitude-throwing",
    "task_id": "142"
  },
  "events": {
    "camera_id": "camera_001",
    "timestamp": 1704110400000,
    "objects": [
      {
        "object_label": "High-altitude-throwing",
        "track_id": "1",
        "object_box": {
          "x": 0.653,
          "y": 0.175,
          "width": 0.025,
          "height": 0.028
        }
      }
    ]
  },
  "processing_time_ms": 45.2,
  "engine_info": {
    "engine": "deepstream",
    "mode": "高性能模式",
    "gpu_accelerated": true
  }
}
```

#### 模式管理接口
```http
GET /api/v1/mode
```
**响应**：当前模式信息和可用模式列表

```http
POST /api/v1/mode/switch
Content-Type: application/json

{
  "target_mode": "python"
}
```
**功能**：手动切换执行模式

## 🔍 故障排除

### 常见问题
```bash
# 检查容器状态
docker ps -a

# 查看容器日志
docker logs [container-name]

# 检查网络连接
docker network inspect algorithm-network

# 重启服务
cd separated-deployment-system/platform-package/algorithm-platform-deploy-package
docker-compose restart
```

### 智能算法包故障排除

#### GPU环境问题
```bash
# 检查GPU状态
nvidia-smi

# 检查CUDA环境
nvcc --version

# 检查DeepStream安装
ls -la /opt/nvidia/deepstream/deepstream/

# 检查Docker GPU支持
docker run --rm --gpus all nvidia/cuda:11.4-base-ubuntu20.04 nvidia-smi
```

#### 模式切换问题
```bash
# 查看当前模式
curl http://localhost:8005/api/v1/mode

# 查看详细健康状态
curl http://localhost:8005/api/v1/health

# 手动切换到Python模式
curl -X POST http://localhost:8005/api/v1/mode/switch \
  -H "Content-Type: application/json" \
  -d '{"target_mode": "python"}'
```

#### 性能问题诊断
- **DeepStream模式慢**: 检查GPU内存使用，确保有足够的GPU内存
- **Python模式慢**: 检查CPU使用率，考虑优化图像处理参数
- **频繁模式切换**: 检查环境稳定性，可能需要固定模式

### 端口冲突处理
```bash
cd algorithms/_scripts
./manage-algorithms.sh
# 选择"5. 端口管理和清理"
```

### 清理临时文件
```bash
# 自动清理（推荐）
./build-algorithm-packages.sh  # 启动时会提示清理

# 手动清理
rm -rf temp-build-*
rm -rf separated-deployment-system/temp-deployment-*
```

## 📚 开发规范

### 算法包开发标准
- 查阅: `algorithms/_standards/development-standards.md`
- 兼容性验证: `algorithms/_standards/compatibility-verification.md`
- 端口管理: `algorithms/_standards/port-mapping.md`

### 智能算法包开发指南

#### 环境检测器实现
```python
from src.environment_detector import EnvironmentDetector

detector = EnvironmentDetector()
env_info = detector.detect_environment()

# 检查推荐模式
if env_info.recommended_mode == "deepstream":
    # 使用GPU加速模式
    pass
else:
    # 使用CPU兼容模式
    pass
```

#### 双引擎架构设计
- **抽象引擎接口**: 统一的检测引擎基类
- **DeepStream引擎**: GPU硬件加速实现
- **Python引擎**: CPU兼容实现
- **智能管理器**: 自动选择和切换引擎

#### 智能启动脚本
- 启动时环境检测和日志输出
- 自动设置环境变量和路径
- 彩色日志显示和进度提示
- 错误处理和降级机制

### 代码质量要求
- 使用uv进行依赖管理
- 遵循FastAPI最佳实践
- 实现必需的健康检查接口
- 提供完整的README文档
- 支持环境自适应和智能切换
- 实现故障恢复和性能监控

### 部署最佳实践

#### GPU环境部署
```bash
# 构建支持GPU的镜像
docker build -t algorithm:gpu .

# 运行时启用GPU支持
docker run --gpus all -p 8005:8005 algorithm:gpu
```

#### CPU环境部署
```bash
# 同样的镜像，自动适配CPU环境
docker run -p 8005:8005 algorithm:gpu
```

#### 开发环境设置
```bash
# 使用uv管理虚拟环境
cd algorithms/your-algorithm
uv sync
uv run uvicorn src.api_server:app --reload --port 8005
```

---

**版本**: 3.0
**更新时间**: 2025-01-01
**维护**: 统一AI算法管理平台团队
**重大更新**: 新增智能算法包架构和环境自适应功能


